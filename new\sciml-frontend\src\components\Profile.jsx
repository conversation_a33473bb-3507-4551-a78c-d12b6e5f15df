import { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import Navbar from './Navbar';
import Footer from './Footer';
import { Link } from 'react-router-dom';

const Profile = () => {
  const { user, updateUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(user?.name || '');
  const [editedTitle, setEditedTitle] = useState(user?.title || '');
  const [editedBio, setEditedBio] = useState(user?.bio || '');

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Here you would typically upload the file to your server
      // and update the user's avatar URL
      const reader = new FileReader();
      reader.onloadend = () => {
        updateUser({ ...user, avatar: reader.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = () => {
    updateUser({
      ...user,
      name: editedName,
      title: editedTitle,
      bio: editedBio
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedName(user?.name || '');
    setEditedTitle(user?.title || '');
    setEditedBio(user?.bio || '');
    setIsEditing(false);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <p className="text-accent">Please log in to view your profile.</p>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="min-h-screen bg-gray-50 pt-16 sm:pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          {/* Profile Header */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-8 mb-6 sm:mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="relative">
                <img
                  src={user?.avatar || '/default-avatar.png'}
                  alt={user?.name}
                  className="w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-highlight"
                />
                {isEditing && (
                  <motion.button
                    className="absolute bottom-0 right-0 p-2 bg-highlight text-white rounded-full shadow-lg hover:bg-opacity-90 transition-colors duration-200"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => document.getElementById('avatar-upload').click()}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </motion.button>
                )}
                <input
                  type="file"
                  id="avatar-upload"
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                />
              </div>
              <div className="flex-1 text-center sm:text-left">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-accent">
                      {isEditing ? (
                        <input
                          type="text"
                          value={editedName}
                          onChange={(e) => setEditedName(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight focus:border-transparent"
                        />
                      ) : (
                        user?.name
                      )}
                    </h1>
                    <p className="text-primary mt-1">
                      {isEditing ? (
                        <input
                          type="text"
                          value={editedTitle}
                          onChange={(e) => setEditedTitle(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight focus:border-transparent"
                        />
                      ) : (
                        user?.title || 'Research Scientist'
                      )}
                    </p>
                  </div>
                  <div className="mt-4 sm:mt-0 flex justify-center sm:justify-end space-x-2">
                    {isEditing ? (
                      <>
                        <motion.button
                          className="px-4 py-2 bg-highlight text-white rounded-lg hover:bg-opacity-90 transition-colors duration-200"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleSave}
                        >
                          Save Changes
                        </motion.button>
                        <motion.button
                          className="px-4 py-2 bg-gray-200 text-accent rounded-lg hover:bg-gray-300 transition-colors duration-200"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleCancel}
                        >
                          Cancel
                        </motion.button>
                      </>
                    ) : (
                      <motion.button
                        className="px-4 py-2 bg-highlight text-white rounded-lg hover:bg-opacity-90 transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setIsEditing(true)}
                      >
                        Edit Profile
                      </motion.button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Profile Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* Left Column - Profile Info */}
            <div className="lg:col-span-2 space-y-6 sm:space-y-8">
              {/* About Section */}
              <motion.div
                className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h2 className="text-lg sm:text-xl font-semibold text-accent mb-4">About</h2>
                {isEditing ? (
                  <textarea
                    value={editedBio}
                    onChange={(e) => setEditedBio(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight focus:border-transparent min-h-[100px]"
                    placeholder="Tell us about yourself..."
                  />
                ) : (
                  <p className="text-primary">{user?.bio || 'No bio available.'}</p>
                )}
              </motion.div>

              {/* Statistics Section */}
              <motion.div
                className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h2 className="text-lg sm:text-xl font-semibold text-accent mb-4">Statistics</h2>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-2xl sm:text-3xl font-bold text-accent">42</h3>
                    <p className="text-primary text-sm">Papers</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-2xl sm:text-3xl font-bold text-accent">15</h3>
                    <p className="text-primary text-sm">Models</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-2xl sm:text-3xl font-bold text-accent">2.8K</h3>
                    <p className="text-primary text-sm">API Calls</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-2xl sm:text-3xl font-bold text-accent">68%</h3>
                    <p className="text-primary text-sm">Storage</p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Right Column - Quick Links */}
            <div className="space-y-6 sm:space-y-8">
              <motion.div
                className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h2 className="text-lg sm:text-xl font-semibold text-accent mb-4">Quick Links</h2>
                <div className="space-y-2">
                  <Link
                    to="/dashboard/upload"
                    className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                  >
                    <svg className="w-5 h-5 text-highlight mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    <span className="text-primary">Upload Paper</span>
                  </Link>
                  <Link
                    to="/dashboard/models/create"
                    className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                  >
                    <svg className="w-5 h-5 text-highlight mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    <span className="text-primary">Create Model</span>
                  </Link>
                  <Link
                    to="/dashboard/analytics/usage"
                    className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                  >
                    <svg className="w-5 h-5 text-highlight mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className="text-primary">View Analytics</span>
                  </Link>
                  <Link
                    to="/dashboard/apis"
                    className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                  >
                    <svg className="w-5 h-5 text-highlight mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-primary">API Keys</span>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Profile;
