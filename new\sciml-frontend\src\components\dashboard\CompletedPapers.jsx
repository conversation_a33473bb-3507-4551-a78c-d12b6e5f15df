import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const CompletedPapers = () => {
  const [completedPapers, setCompletedPapers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [selectedPapers, setSelectedPapers] = useState([]);

  // Mock data for completed papers
  useEffect(() => {
    const mockCompletedPapers = [
      {
        id: 1,
        title: 'Deep Learning Approaches for Climate Modeling',
        authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
        uploadDate: '2024-01-15',
        completedDate: '2024-01-15T14:30:00Z',
        processingTime: '2.5 hours',
        fileSize: '4.2 MB',
        models: 3,
        apis: 2,
        downloads: 156,
        views: 342,
        tags: ['Climate', 'Deep Learning', 'Neural Networks'],
        accuracy: 94.2,
        performance: 'Excellent',
        generatedAssets: {
          models: ['Climate_CNN_v1', 'Temperature_LSTM', 'Precipitation_GAN'],
          apis: ['climate-prediction-api', 'weather-forecast-api'],
          datasets: ['processed_climate_data.csv', 'feature_vectors.npy'],
          documentation: ['model_documentation.pdf', 'api_reference.md']
        }
      },
      {
        id: 3,
        title: 'Machine Learning for Protein Folding Prediction',
        authors: ['Prof. Alex Johnson', 'Dr. Maria Garcia'],
        uploadDate: '2024-01-13',
        completedDate: '2024-01-13T16:45:00Z',
        processingTime: '3.1 hours',
        fileSize: '8.9 MB',
        models: 5,
        apis: 3,
        downloads: 289,
        views: 567,
        tags: ['Protein Folding', 'Machine Learning', 'Bioinformatics'],
        accuracy: 91.8,
        performance: 'Very Good',
        generatedAssets: {
          models: ['ProteinFold_Transformer', 'Secondary_Structure_CNN', 'Tertiary_Structure_RNN', 'Contact_Map_GCN', 'Fold_Classification_RF'],
          apis: ['protein-fold-api', 'structure-prediction-api', 'contact-map-api'],
          datasets: ['protein_sequences.fasta', 'structure_features.h5', 'contact_maps.npz'],
          documentation: ['protein_folding_guide.pdf', 'api_endpoints.md', 'model_comparison.xlsx']
        }
      },
      {
        id: 5,
        title: 'Computer Vision for Medical Image Analysis',
        authors: ['Dr. David Lee', 'Prof. Anna Smith'],
        uploadDate: '2024-01-11',
        completedDate: '2024-01-11T18:20:00Z',
        processingTime: '4.2 hours',
        fileSize: '12.3 MB',
        models: 7,
        apis: 4,
        downloads: 423,
        views: 789,
        tags: ['Computer Vision', 'Medical Imaging', 'CNN'],
        accuracy: 96.7,
        performance: 'Outstanding',
        generatedAssets: {
          models: ['MedImage_ResNet', 'Tumor_Detection_YOLO', 'Segmentation_UNet', 'Classification_EfficientNet', 'Anomaly_Detection_VAE', 'Feature_Extraction_VGG', 'Diagnosis_Ensemble'],
          apis: ['medical-image-api', 'tumor-detection-api', 'image-segmentation-api', 'diagnosis-api'],
          datasets: ['medical_images.zip', 'annotations.json', 'preprocessed_data.h5', 'augmented_dataset.tar.gz'],
          documentation: ['medical_ai_guide.pdf', 'model_architectures.md', 'deployment_guide.pdf', 'ethics_guidelines.pdf']
        }
      },
      {
        id: 8,
        title: 'Natural Language Processing for Scientific Literature',
        authors: ['Dr. Jennifer Wu', 'Prof. Thomas Brown'],
        uploadDate: '2024-01-10',
        completedDate: '2024-01-10T20:15:00Z',
        processingTime: '2.8 hours',
        fileSize: '6.7 MB',
        models: 4,
        apis: 3,
        downloads: 198,
        views: 445,
        tags: ['NLP', 'Scientific Literature', 'BERT'],
        accuracy: 89.3,
        performance: 'Good',
        generatedAssets: {
          models: ['SciBERT_Classifier', 'Citation_Extractor', 'Abstract_Summarizer', 'Topic_Modeler'],
          apis: ['literature-analysis-api', 'citation-extraction-api', 'text-summarization-api'],
          datasets: ['scientific_papers.json', 'processed_abstracts.csv', 'citation_network.gml'],
          documentation: ['nlp_methodology.pdf', 'api_usage_examples.md', 'model_evaluation.xlsx']
        }
      }
    ];

    setTimeout(() => {
      setCompletedPapers(mockCompletedPapers);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort papers
  const filteredPapers = completedPapers
    .filter(paper => {
      const matchesSearch = paper.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           paper.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
                           paper.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.completedDate) - new Date(a.completedDate);
        case 'title':
          return a.title.localeCompare(b.title);
        case 'accuracy':
          return b.accuracy - a.accuracy;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'models':
          return b.models - a.models;
        default:
          return 0;
      }
    });

  const getPerformanceColor = (performance) => {
    switch (performance) {
      case 'Outstanding':
        return 'bg-green-100 text-green-800';
      case 'Excellent':
        return 'bg-blue-100 text-blue-800';
      case 'Very Good':
        return 'bg-purple-100 text-purple-800';
      case 'Good':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSelectPaper = (paperId) => {
    setSelectedPapers(prev =>
      prev.includes(paperId)
        ? prev.filter(id => id !== paperId)
        : [...prev, paperId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPapers.length === filteredPapers.length) {
      setSelectedPapers([]);
    } else {
      setSelectedPapers(filteredPapers.map(paper => paper.id));
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Completed Papers</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Successfully processed papers with generated models and APIs
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/papers"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                All Papers
              </Link>
              <Link
                to="/dashboard/upload"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Upload New
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Total Completed',
              value: completedPapers.length,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              )
            },
            {
              label: 'Total Models',
              value: completedPapers.reduce((sum, paper) => sum + paper.models, 0),
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              )
            },
            {
              label: 'Total APIs',
              value: completedPapers.reduce((sum, paper) => sum + paper.apis, 0),
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
                </svg>
              )
            },
            {
              label: 'Total Downloads',
              value: completedPapers.reduce((sum, paper) => sum + paper.downloads, 0),
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search completed papers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Sort */}
            <div className="flex space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Completion Date</option>
                <option value="title">Sort by Title</option>
                <option value="accuracy">Sort by Accuracy</option>
                <option value="downloads">Sort by Downloads</option>
                <option value="models">Sort by Models Count</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Completed Papers List */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {loading ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading completed papers...</p>
            </div>
          ) : filteredPapers.length === 0 ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No completed papers found</h3>
              <p className="mt-2 text-primary">
                {searchQuery
                  ? 'Try adjusting your search criteria'
                  : 'Upload and process papers to see them here when completed'
                }
              </p>
              {!searchQuery && (
                <Link
                  to="/upload-paper"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Upload Paper
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Bulk Actions */}
              {selectedPapers.length > 0 && (
                <motion.div
                  className="bg-blue-50 border border-blue-200 rounded-xl p-4"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-800">
                      {selectedPapers.length} paper{selectedPapers.length > 1 ? 's' : ''} selected
                    </span>
                    <div className="flex space-x-2">
                      <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        Export Selected
                      </button>
                      <button className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                        Download Models
                      </button>
                      <button className="px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                        Delete Selected
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}

              <AnimatePresence>
                {filteredPapers.map((paper, index) => (
                  <motion.div
                    key={paper.id}
                    className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
                    variants={itemVariants}
                    initial="hidden"
                    animate="visible"
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="p-4 lg:p-6">
                      {/* Paper Header */}
                      <div className="flex items-start space-x-4 mb-6">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded mt-1"
                          checked={selectedPapers.includes(paper.id)}
                          onChange={() => handleSelectPaper(paper.id)}
                        />

                        <div className="flex-1">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                            <div className="flex-1">
                              <h3 className="text-lg lg:text-xl font-semibold text-accent hover:text-highlight transition-colors duration-200">
                                <Link to={`/dashboard/papers/${paper.id}`}>
                                  {paper.title}
                                </Link>
                              </h3>
                              <p className="text-sm text-primary mt-1">
                                by {paper.authors.join(', ')}
                              </p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {paper.tags.map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>

                            <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(paper.performance)}`}>
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                                {paper.performance}
                              </div>
                              <div className="text-xs text-primary">
                                Completed: {new Date(paper.completedDate).toLocaleDateString()}
                              </div>
                              <div className="text-xs font-medium text-highlight">
                                {paper.accuracy}% Accuracy
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Generated Assets Summary */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-blue-600 font-medium">Models</p>
                              <p className="text-lg font-bold text-blue-800">{paper.models}</p>
                            </div>
                            <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-green-600 font-medium">APIs</p>
                              <p className="text-lg font-bold text-green-800">{paper.apis}</p>
                            </div>
                            <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-purple-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-purple-600 font-medium">Downloads</p>
                              <p className="text-lg font-bold text-purple-800">{paper.downloads}</p>
                            </div>
                            <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-yellow-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-yellow-600 font-medium">Views</p>
                              <p className="text-lg font-bold text-yellow-800">{paper.views}</p>
                            </div>
                            <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </div>
                        </div>
                      </div>

                      {/* Generated Assets Details */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-accent mb-3">Generated Assets</h4>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {/* Models */}
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h5 className="text-xs font-medium text-accent mb-2 flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                              </svg>
                              Models ({paper.generatedAssets.models.length})
                            </h5>
                            <div className="space-y-1">
                              {paper.generatedAssets.models.slice(0, 3).map((model, idx) => (
                                <div key={idx} className="text-xs text-primary bg-white rounded px-2 py-1">
                                  {model}
                                </div>
                              ))}
                              {paper.generatedAssets.models.length > 3 && (
                                <div className="text-xs text-highlight">
                                  +{paper.generatedAssets.models.length - 3} more
                                </div>
                              )}
                            </div>
                          </div>

                          {/* APIs */}
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h5 className="text-xs font-medium text-accent mb-2 flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
                              </svg>
                              APIs ({paper.generatedAssets.apis.length})
                            </h5>
                            <div className="space-y-1">
                              {paper.generatedAssets.apis.map((api, idx) => (
                                <div key={idx} className="text-xs text-primary bg-white rounded px-2 py-1">
                                  {api}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Paper Details */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm mb-6">
                        <div>
                          <span className="text-primary">File Size:</span>
                          <span className="ml-1 font-medium text-accent">{paper.fileSize}</span>
                        </div>
                        <div>
                          <span className="text-primary">Processing Time:</span>
                          <span className="ml-1 font-medium text-accent">{paper.processingTime}</span>
                        </div>
                        <div>
                          <span className="text-primary">Uploaded:</span>
                          <span className="ml-1 font-medium text-accent">
                            {new Date(paper.uploadDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div>
                          <span className="text-primary">Completed:</span>
                          <span className="ml-1 font-medium text-accent">
                            {new Date(paper.completedDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                        <Link
                          to={`/dashboard/papers/${paper.id}`}
                          className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                        >
                          View Details
                        </Link>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                          Download Models
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                          API Documentation
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                          Export Data
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 hover:text-green-800 transition-colors duration-200">
                          Deploy Models
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                          Delete
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default CompletedPapers;