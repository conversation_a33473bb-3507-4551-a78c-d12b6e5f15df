import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UserDropdown from './UserDropdown';
import './styles/Navbar.css';

const Navbar = () => {
  const { isAuthenticated } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [typedText, setTypedText] = useState('');
  const [activeItem, setActiveItem] = useState('Home');
  const fullText = 'SciML';

  // Navigation items
  const navItems = [
    { name: 'Home', href: '/' },
    { name: 'Upload Paper', href: '/dashboard/upload' },
    { name: 'Insights', href: '/dashboard/analytics/usage' },
    { name: 'APIs', href: '/dashboard/apis' },
    { name: 'Contact', href: '/contact' }
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    // Set active menu item based on current path
    const handleActiveItem = () => {
      const path = window.location.pathname;

      // Find the matching nav item
      const matchingItem = navItems.find(item => item.href === path);

      if (matchingItem) {
        setActiveItem(matchingItem.name);
      } else if (path === '/login' || path === '/signup') {
        // Don't highlight any nav item on auth pages
        setActiveItem('');
      } else {
        // Default to Home if no match
        setActiveItem('Home');
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Call handleActiveItem on mount and when location changes
    handleActiveItem();
    window.addEventListener('popstate', handleActiveItem);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('popstate', handleActiveItem);
    };
  }, [navItems]);

  // Typing animation effect
  useEffect(() => {
    if (typedText.length < fullText.length) {
      const timeout = setTimeout(() => {
        setTypedText(fullText.substring(0, typedText.length + 1));
      }, 150);

      return () => clearTimeout(timeout);
    }
  }, [typedText]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'navbar-gradient shadow-lg' : 'navbar-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <motion.div
              className="flex-shrink-0 flex items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Link to="/" className="flex items-center">
                <img className="h-8 sm:h-10 w-auto mr-2 logo-pulse" src="/logo_2.png" alt="SciML Logo" />
                <span className="text-xl sm:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-highlight">
                  {typedText}<span className="animate-pulse">|</span>
                </span>
              </Link>
            </motion.div>

            <div className="hidden md:ml-6 md:flex md:items-baseline md:space-x-4 lg:space-x-6">
              {navItems.map((item, index) => (
                <Link to={item.href} key={item.name}>
                  <motion.span
                    className={`nav-link px-2 lg:px-3 py-2 rounded-md text-sm font-medium text-white hover:text-highlight transition-colors duration-200 ${
                      activeItem === item.name ? 'active' : ''
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * (index + 1) }}
                    onClick={() => setActiveItem(item.name)}
                  >
                    {item.name}
                  </motion.span>
                </Link>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <UserDropdown />
            ) : (
              <Link to="/login">
                <motion.button
                  className="hidden md:inline-flex items-center px-3 lg:px-5 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-highlight hover:bg-opacity-90 transition-all duration-300 btn-glow"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  <span className="hidden lg:inline">Login / Sign Up</span>
                  <span className="lg:hidden">Login</span>
                </motion.button>
              </Link>
            )}

            {/* Mobile menu button */}
            <div className="md:hidden">
              <motion.button
                onClick={toggleMobileMenu}
                className="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-highlight focus:outline-none"
                whileTap={{ scale: 0.95 }}
              >
                <span className="sr-only">Open main menu</span>
                {isMobileMenuOpen ? (
                  <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </motion.button>
            </div>
          </div>


        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="md:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-4 pt-4 pb-6 space-y-3 dropdown-menu rounded-b-lg">
              {navItems.map((item) => (
                <Link to={item.href} key={item.name} className="block">
                  <motion.div
                    className={`dropdown-item block px-4 py-3 rounded-lg text-base font-medium text-white hover:text-highlight hover:bg-white hover:bg-opacity-10 transition-all duration-200 ${
                      activeItem === item.name ? 'bg-highlight bg-opacity-20 text-highlight' : ''
                    }`}
                    onClick={() => {
                      setActiveItem(item.name);
                      setIsMobileMenuOpen(false);
                    }}
                    whileHover={{ x: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {item.name}
                  </motion.div>
                </Link>
              ))}

              {/* Mobile auth section */}
              <div className="pt-4 border-t border-white border-opacity-20">
                {isAuthenticated ? (
                  <div className="px-4">
                    <UserDropdown />
                  </div>
                ) : (
                  <Link to="/login" className="block">
                    <motion.div
                      className="dropdown-item flex items-center px-4 py-3 rounded-lg text-base font-medium text-white bg-highlight hover:bg-opacity-90 transition-all duration-200"
                      onClick={() => setIsMobileMenuOpen(false)}
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Login / Sign Up
                    </motion.div>
                  </Link>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navbar;
