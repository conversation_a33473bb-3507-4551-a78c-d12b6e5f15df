/* Contact.css - Styles for the Contact component */

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Input focus effect */
input:focus, textarea:focus {
  box-shadow: 0 0 0 2px rgba(0, 181, 173, 0.5);
}

/* Social icon hover animation */
@keyframes social-float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

.social-icon:hover {
  animation: social-float 1s ease-in-out infinite;
}

/* Contact info item hover effect */
.contact-info-item {
  transition: all 0.3s ease;
}

.contact-info-item:hover {
  transform: translateX(5px);
}

/* Button glow effect */
.btn-glow:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.6);
}

/* Form input animation */
@keyframes input-focus {
  0% {
    border-color: rgba(255, 255, 255, 0.2);
  }
  100% {
    border-color: rgba(0, 181, 173, 1);
  }
}

.form-input:focus {
  animation: input-focus 0.3s forwards;
}
