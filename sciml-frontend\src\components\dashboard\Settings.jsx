import { useState } from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../DashboardLayout';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('profile');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const tabs = [
    { id: 'profile', name: 'Profile' },
    { id: 'account', name: 'Account' },
    { id: 'notifications', name: 'Notifications' },
    { id: 'security', name: 'Security' },
    { id: 'preferences', name: 'Preferences' }
  ];

  const mockUserData = {
    profile: {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Research Scientist',
      organization: 'SciML Research Lab',
      bio: 'Passionate about machine learning and scientific research.',
      avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=random'
    },
    account: {
      plan: 'Professional',
      status: 'Active',
      nextBilling: '2024-04-01',
      apiUsage: '75%',
      storageUsage: '45%'
    },
    notifications: {
      email: {
        updates: true,
        security: true,
        marketing: false
      },
      push: {
        updates: true,
        security: true,
        marketing: false
      }
    },
    security: {
      twoFactor: true,
      lastPasswordChange: '2024-02-15',
      activeDevices: 3,
      loginHistory: [
        { date: '2024-03-01', device: 'Chrome on Windows', location: 'New York, US' },
        { date: '2024-02-28', device: 'Safari on MacOS', location: 'Boston, US' },
        { date: '2024-02-27', device: 'Firefox on Linux', location: 'San Francisco, US' }
      ]
    },
    preferences: {
      theme: 'light',
      language: 'English',
      timezone: 'America/New_York',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12-hour'
    }
  };

  return (
    <DashboardLayout>
      <div className="pt-16 sm:pt-20 px-4 lg:px-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Settings</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Manage your account settings and preferences
              </p>
            </div>
          </div>
        </motion.div>

        {/* Statistics Overview */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Account Status',
              value: mockUserData.account.status,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            },
            {
              label: 'Last Login',
              value: mockUserData.security.loginHistory[0].date,
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            },
            {
              label: 'Notifications',
              value: `${Object.values(mockUserData.notifications.email).filter(Boolean).length} Active`,
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              )
            },
            {
              label: 'Security Level',
              value: mockUserData.security.twoFactor ? 'High' : 'Medium',
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 mb-6 lg:mb-8">
          <div className="p-4 border-b border-gray-200">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'bg-highlight text-white'
                      : 'bg-gray-100 text-primary hover:bg-gray-200'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="p-6">
            {activeTab === 'profile' && (
              <div>
                <h2 className="text-lg font-semibold text-accent mb-6">Profile Information</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Full Name</label>
                      <input
                        type="text"
                        value={mockUserData.profile.name}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Email</label>
                      <input
                        type="email"
                        value={mockUserData.profile.email}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Role</label>
                      <input
                        type="text"
                        value={mockUserData.profile.role}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Organization</label>
                      <input
                        type="text"
                        value={mockUserData.profile.organization}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Bio</label>
                      <textarea
                        value={mockUserData.profile.bio}
                        rows="4"
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      ></textarea>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Profile Picture</label>
                      <div className="flex items-center space-x-4">
                        <img
                          src={mockUserData.profile.avatar}
                          alt="Profile"
                          className="w-20 h-20 rounded-full"
                        />
                        <button className="px-4 py-2 bg-gray-200 text-primary rounded-lg text-sm font-medium hover:bg-gray-300 transition-colors duration-200">
                          Change Picture
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-2 bg-highlight text-white rounded-lg text-sm font-medium hover:bg-highlight/90 transition-colors duration-200">
                    Save Changes
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'account' && (
              <div>
                <h2 className="text-lg font-semibold text-accent mb-6">Account Settings</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Current Plan</label>
                      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <span className="text-accent font-medium">{mockUserData.account.plan}</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                          {mockUserData.account.status}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Next Billing Date</label>
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <span className="text-accent">{mockUserData.account.nextBilling}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">API Usage</label>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-primary">Usage</span>
                          <span className="text-accent">{mockUserData.account.apiUsage}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-highlight h-2 rounded-full"
                            style={{ width: mockUserData.account.apiUsage }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Storage Usage</label>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-primary">Usage</span>
                          <span className="text-accent">{mockUserData.account.storageUsage}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-highlight h-2 rounded-full"
                            style={{ width: mockUserData.account.storageUsage }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-2 bg-highlight text-white rounded-lg text-sm font-medium hover:bg-highlight/90 transition-colors duration-200">
                    Upgrade Plan
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div>
                <h2 className="text-lg font-semibold text-accent mb-6">Notification Preferences</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-md font-medium text-accent mb-4">Email Notifications</h3>
                    <div className="space-y-4">
                      {Object.entries(mockUserData.notifications.email).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <label className="text-primary">
                            {key.charAt(0).toUpperCase() + key.slice(1)} Updates
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={value}
                              className="form-checkbox text-highlight"
                            />
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-md font-medium text-accent mb-4">Push Notifications</h3>
                    <div className="space-y-4">
                      {Object.entries(mockUserData.notifications.push).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <label className="text-primary">
                            {key.charAt(0).toUpperCase() + key.slice(1)} Updates
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="checkbox"
                              checked={value}
                              className="form-checkbox text-highlight"
                            />
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-2 bg-highlight text-white rounded-lg text-sm font-medium hover:bg-highlight/90 transition-colors duration-200">
                    Save Preferences
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div>
                <h2 className="text-lg font-semibold text-accent mb-6">Security Settings</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Two-Factor Authentication</label>
                      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <span className="text-accent">
                          {mockUserData.security.twoFactor ? 'Enabled' : 'Disabled'}
                        </span>
                        <button className="px-4 py-2 bg-gray-200 text-primary rounded-lg text-sm font-medium hover:bg-gray-300 transition-colors duration-200">
                          {mockUserData.security.twoFactor ? 'Disable' : 'Enable'}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Last Password Change</label>
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <span className="text-accent">{mockUserData.security.lastPasswordChange}</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Active Devices</label>
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <span className="text-accent">{mockUserData.security.activeDevices} devices</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-md font-medium text-accent mb-4">Recent Login History</h3>
                    <div className="space-y-4">
                      {mockUserData.security.loginHistory.map((login, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="text-accent">{login.device}</p>
                            <p className="text-sm text-primary">{login.location}</p>
                          </div>
                          <span className="text-sm text-gray-500">{login.date}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-2 bg-highlight text-white rounded-lg text-sm font-medium hover:bg-highlight/90 transition-colors duration-200">
                    Change Password
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'preferences' && (
              <div>
                <h2 className="text-lg font-semibold text-accent mb-6">User Preferences</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Theme</label>
                      <select
                        value={mockUserData.preferences.theme}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      >
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="system">System</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Language</label>
                      <select
                        value={mockUserData.preferences.language}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      >
                        <option value="English">English</option>
                        <option value="Spanish">Spanish</option>
                        <option value="French">French</option>
                        <option value="German">German</option>
                      </select>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Timezone</label>
                      <select
                        value={mockUserData.preferences.timezone}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      >
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Date Format</label>
                      <select
                        value={mockUserData.preferences.dateFormat}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      >
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-primary mb-2">Time Format</label>
                      <select
                        value={mockUserData.preferences.timeFormat}
                        className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight"
                      >
                        <option value="12-hour">12-hour</option>
                        <option value="24-hour">24-hour</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div className="mt-6">
                  <button className="px-6 py-2 bg-highlight text-white rounded-lg text-sm font-medium hover:bg-highlight/90 transition-colors duration-200">
                    Save Preferences
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default Settings; 