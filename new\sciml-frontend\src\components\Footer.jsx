import { motion, useAnimation } from 'framer-motion';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import './styles/Footer.css';

const Footer = () => {
  // Animation controls
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    }
  };

  const glowVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 0.6,
      transition: {
        duration: 1.5,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }
    }
  };

  const floatVariants = {
    hidden: { y: 0 },
    visible: {
      y: [-10, 10, -10],
      transition: {
        duration: 6,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut"
      }
    }
  };

  // Social media icons with modern styling
  const socialLinks = [
    {
      name: 'Twitter',
      icon: 'M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z',
      href: '#',
      color: 'from-blue-400 to-blue-600'
    },
    {
      name: 'LinkedIn',
      icon: 'M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z M4 6a2 2 0 100-4 2 2 0 000 4z',
      href: '#',
      color: 'from-blue-600 to-blue-800'
    },
    {
      name: 'GitHub',
      icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z',
      href: '#',
      color: 'from-gray-700 to-gray-900'
    },
  ];

  return (
    <motion.footer
      ref={ref}
      className="relative overflow-hidden bg-gradient-to-br from-primary via-accent to-primary text-white py-16"
      initial="hidden"
      animate={controls}
      variants={containerVariants}
    >
      {/* Decorative elements */}
      <motion.div
        className="absolute top-0 right-0 w-96 h-96 bg-highlight opacity-10 rounded-full blur-3xl"
        variants={glowVariants}
      />
      <motion.div
        className="absolute bottom-0 left-0 w-80 h-80 bg-accent opacity-10 rounded-full blur-3xl"
        variants={glowVariants}
      />
      <motion.div
        className="absolute top-1/2 left-1/4 w-40 h-40 bg-white opacity-5 rounded-full blur-2xl"
        variants={floatVariants}
      />

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Logo and description */}
          <motion.div className="col-span-1 md:col-span-1" variants={itemVariants}>
            <div className="flex items-center">
              <div className="relative">
                <motion.div
                  className="absolute inset-0 bg-highlight opacity-20 blur-md rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.2, 0.3, 0.2]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <img className="h-12 w-auto relative z-10" src="/logo_2.png" alt="SciML" />
              </div>
              <span className="ml-3 text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white via-highlight to-white">
                SciML
              </span>
            </div>

            <motion.div
              className="mt-6 p-4 bg-white bg-opacity-5 backdrop-blur-sm rounded-lg border border-white border-opacity-10"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <p className="text-sm text-gray-100">
                Transforming scientific research into interactive tools and APIs through advanced machine learning.
              </p>
            </motion.div>

            {/* Social links */}
            <div className="mt-8 flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  className="group relative flex items-center justify-center w-10 h-10 rounded-full bg-white bg-opacity-10 hover:bg-opacity-20 transition-all duration-300"
                  aria-label={social.name}
                  whileHover={{ scale: 1.1, y: -5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className={`absolute inset-0 rounded-full bg-gradient-to-tr ${social.color} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                  />
                  <svg className="h-5 w-5 relative z-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d={social.icon} />
                  </svg>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick links */}
          <motion.div className="col-span-1" variants={itemVariants}>
            <motion.div
              className="p-5 rounded-xl bg-white bg-opacity-5 backdrop-blur-sm border border-white border-opacity-10 h-full"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-highlight to-white tracking-wider uppercase">Platform</h3>
              <ul className="mt-6 space-y-4">
                {['Home', 'Features', 'Upload Paper', 'Pricing', 'Case Studies'].map((item) => (
                  <motion.li key={item} whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    <a
                      href={`#${item.toLowerCase().replace(' ', '-')}`}
                      className="text-base text-gray-200 hover:text-highlight flex items-center group transition-all duration-300"
                    >
                      <span className="w-0 group-hover:w-2 h-0.5 bg-highlight mr-0 group-hover:mr-2 transition-all duration-300"></span>
                      {item}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </motion.div>

          {/* Resources */}
          <motion.div className="col-span-1" variants={itemVariants}>
            <motion.div
              className="p-5 rounded-xl bg-white bg-opacity-5 backdrop-blur-sm border border-white border-opacity-10 h-full"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-highlight to-white tracking-wider uppercase">Resources</h3>
              <ul className="mt-6 space-y-4">
                {['Documentation', 'API Reference', 'Tutorials', 'Blog', 'Research Papers'].map((item) => (
                  <motion.li key={item} whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    <a
                      href={`#${item.toLowerCase().replace(' ', '-')}`}
                      className="text-base text-gray-200 hover:text-highlight flex items-center group transition-all duration-300"
                    >
                      <span className="w-0 group-hover:w-2 h-0.5 bg-highlight mr-0 group-hover:mr-2 transition-all duration-300"></span>
                      {item}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </motion.div>

          {/* Company */}
          <motion.div className="col-span-1" variants={itemVariants}>
            <motion.div
              className="p-5 rounded-xl bg-white bg-opacity-5 backdrop-blur-sm border border-white border-opacity-10 h-full"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-highlight to-white tracking-wider uppercase">Company</h3>
              <ul className="mt-6 space-y-4">
                {['About', 'Team', 'Careers', 'Contact', 'Privacy Policy'].map((item) => (
                  <motion.li key={item} whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                    <a
                      href={`#${item.toLowerCase().replace(' ', '-')}`}
                      className="text-base text-gray-200 hover:text-highlight flex items-center group transition-all duration-300"
                    >
                      <span className="w-0 group-hover:w-2 h-0.5 bg-highlight mr-0 group-hover:mr-2 transition-all duration-300"></span>
                      {item}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </motion.div>
        </div>

        {/* Newsletter subscription */}
        <motion.div
          className="mt-16 pt-8 border-t border-white border-opacity-10"
          variants={itemVariants}
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0 max-w-lg">
              <motion.h3
                className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white to-highlight tracking-wide"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                Subscribe to our newsletter
              </motion.h3>
              <p className="mt-3 text-base text-gray-200">
                Stay updated with the latest in scientific machine learning and be the first to know about new features and research breakthroughs.
              </p>

              {/* Image placeholder for newsletter section */}
              <motion.div
                className="mt-6 hidden md:block relative h-24 w-48 overflow-hidden rounded-lg"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-highlight to-accent opacity-70 z-10"></div>
                <img
                  src="/footer-image-placeholder.svg"
                  alt="Newsletter illustration"
                  className="h-full w-full object-cover"
                />
                <div className="absolute inset-0 flex items-center justify-center z-20">
                  <span className="text-white font-bold text-sm">Latest Research Updates</span>
                </div>
              </motion.div>
            </div>

            <motion.div
              className="w-full md:w-auto bg-white bg-opacity-5 p-1 rounded-lg backdrop-blur-sm border border-white border-opacity-10"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex flex-col sm:flex-row">
                <div className="relative">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="px-4 py-3 w-full sm:w-64 rounded-t-md sm:rounded-l-md sm:rounded-r-none bg-white bg-opacity-10 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-highlight border-none"
                  />
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-highlight to-accent"
                    initial={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <motion.button
                  className="px-6 py-3 bg-gradient-to-r from-highlight to-accent text-white rounded-b-md sm:rounded-r-md sm:rounded-l-none hover:shadow-lg transition-all duration-300 font-medium"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 0 15px rgba(0, 181, 173, 0.5)"
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="flex items-center justify-center">
                    Subscribe
                    <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </span>
                </motion.button>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Copyright */}
        <motion.div
          className="mt-12 pt-6 border-t border-white border-opacity-5 text-center"
          variants={itemVariants}
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-300">
              &copy; {new Date().getFullYear()} SciML. All rights reserved. Transforming research through AI.
            </p>
            <div className="mt-4 md:mt-0">
              <motion.a
                href="#terms"
                className="text-sm text-gray-300 hover:text-highlight mx-3 transition-colors duration-200"
                whileHover={{ y: -2 }}
                transition={{ duration: 0.2 }}
              >
                Terms
              </motion.a>
              <motion.a
                href="#privacy"
                className="text-sm text-gray-300 hover:text-highlight mx-3 transition-colors duration-200"
                whileHover={{ y: -2 }}
                transition={{ duration: 0.2 }}
              >
                Privacy
              </motion.a>
              <motion.a
                href="#cookies"
                className="text-sm text-gray-300 hover:text-highlight mx-3 transition-colors duration-200"
                whileHover={{ y: -2 }}
                transition={{ duration: 0.2 }}
              >
                Cookies
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;