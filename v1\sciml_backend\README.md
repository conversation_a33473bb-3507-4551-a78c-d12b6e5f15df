# SciML Backend

A Flask-based backend API for the SciML platform that provides document analysis and processing capabilities.

## Features

- **PDF Upload & Analysis**: Upload PDF files for comprehensive analysis
- **URL Processing**: Analyze documents from URLs (ArXiv, direct PDF links)
- **Real-time Status Tracking**: Monitor processing progress in real-time
- **Comprehensive Extraction**: Extract text, images, tables, equations, and metadata
- **NLP Analysis**: TF-IDF keywords, named entity recognition, scientific terms
- **Visual Content Processing**: Image extraction with OCR capabilities
- **Persistent Storage**: Results and status stored on disk

## API Endpoints

### Core Endpoints

- `POST /api/upload` - Upload PDF file for analysis
- `POST /api/analyze-url` - Analyze document from URL
- `GET /api/status/<task_id>` - Get processing status
- `GET /api/results/<task_id>` - Get analysis results

### Utility Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `GET /api/debug/tasks` - List all tasks (debug)

## Installation

1. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Download spaCy model**:
   ```bash
   python -m spacy download en_core_web_sm
   ```

## Usage

1. **Start the server**:
   ```bash
   python app.py
   ```

2. **Access the API**:
   - Server runs on `http://localhost:5000`
   - Health check: `http://localhost:5000/health`

## Configuration

### Environment Variables

- `FLASK_ENV`: Set to `development` for debug mode
- `FLASK_PORT`: Port number (default: 5000)

### Storage

The backend creates the following directories:
- `storage/status/` - Processing status files
- `storage/results/` - Analysis results

## API Usage Examples

### Upload File

```bash
curl -X POST -F "file=@document.pdf" http://localhost:5000/api/upload
```

### Analyze URL

```bash
curl -X POST -H "Content-Type: application/json" \
     -d '{"url":"https://arxiv.org/abs/2505.17303"}' \
     http://localhost:5000/api/analyze-url
```

### Check Status

```bash
curl http://localhost:5000/api/status/TASK_ID
```

### Get Results

```bash
curl http://localhost:5000/api/results/TASK_ID
```

## Response Format

### Status Response
```json
{
  "status": "processing|completed|error",
  "message": "Current processing step",
  "progress": 75,
  "started_at": "2024-01-15T10:30:00",
  "results": {...}  // Only when completed
}
```

### Results Response
```json
{
  "metadata": {
    "title": "Document Title",
    "author": "Author Name",
    "pages": 10
  },
  "insights": {
    "tfidf_keywords": [["keyword", 0.95], ...],
    "named_entities": [{"text": "Entity", "label": "PERSON"}, ...]
  },
  "images": [...],
  "tables": [...],
  "text": "Full document text"
}
```

## Features

### Document Processing
- PDF text extraction with PyMuPDF and pdfplumber
- Metadata extraction (title, author, creation date)
- Table detection and extraction
- Image extraction with base64 encoding
- Mathematical equation detection

### NLP Analysis
- TF-IDF keyword extraction
- Named entity recognition with spaCy
- Scientific term identification
- Text statistics and readability scores

### Visual Content
- Image classification (chart, diagram, figure)
- OCR text extraction from images (EasyOCR/Tesseract)
- Table structure analysis
- Visual content categorization

### URL Support
- ArXiv URL automatic conversion to PDF
- Direct PDF URL download
- Robust error handling for network issues

## Error Handling

The API provides comprehensive error handling:
- File validation (PDF only, size limits)
- Network timeout handling
- Processing timeouts (5-10 minutes)
- Graceful degradation when OCR fails

## Development

### Running in Development Mode

```bash
export FLASK_ENV=development
python app.py
```

### Testing

```bash
pytest
```

## Deployment

For production deployment:

1. Use a WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

2. Configure reverse proxy (nginx)
3. Set up proper logging and monitoring
4. Configure file upload limits
5. Set up SSL/TLS certificates

## Troubleshooting

### Common Issues

1. **OCR not working**: Install EasyOCR or Tesseract
2. **spaCy model missing**: Run `python -m spacy download en_core_web_sm`
3. **Memory issues**: Reduce concurrent processing or increase system memory
4. **Timeout errors**: Increase timeout values in app.py

### Logs

Check `sciml_platform.log` for detailed processing logs.

## License

MIT License - see LICENSE file for details.
