/* Home.css - Styles for the Home component */

/* Animated background elements */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-dots-pattern {
  background-image: radial-gradient(rgba(0, 181, 173, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Animated gradient backgrounds */
.animated-gradient {
  background: linear-gradient(-45deg, #003f5c, #2f4b7c, #00b5ad, #003f5c);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating animation */
.floating {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Pulse glow animation */
.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 181, 173, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0);
  }
}

/* Glass morphism */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
}

/* Timeline connector animation */
.timeline-connector {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 181, 173, 0.3), rgba(47, 75, 124, 0.3));
  z-index: 0;
  overflow: hidden;
}

.timeline-connector::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 181, 173, 1), rgba(47, 75, 124, 1));
  animation: timeline-progress 3s ease-in-out infinite;
  transform: translateY(-100%);
}

@keyframes timeline-progress {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #00b5ad, #2f4b7c);
}

/* Futuristic button styles */
.btn-futuristic {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-futuristic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.btn-futuristic:hover::before {
  left: 100%;
}

/* Testimonial card styles */
.testimonial-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #00b5ad, transparent, #2f4b7c);
  z-index: -1;
  border-radius: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover::before {
  opacity: 1;
}

/* Feature card hover effect */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-icon {
  transition: all 0.3s ease;
}
