import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

// API service for SciML backend
const API_BASE_URL = 'http://localhost:5000';

const apiService = {
  getAllTasks: async () => {
    const response = await fetch(`${API_BASE_URL}/api/debug/tasks`);
    if (!response.ok) {
      throw new Error('Failed to fetch tasks');
    }
    return response.json();
  },

  getResults: async (taskId) => {
    const response = await fetch(`${API_BASE_URL}/api/results/${taskId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch results');
    }
    return response.json();
  }
};

// Helper function to extract tags from results
const extractTags = (results) => {
  const tags = ['Document Analysis'];

  if (results.insights?.tfidf_keywords?.length > 0) {
    // Add top keywords as tags
    const topKeywords = results.insights.tfidf_keywords.slice(0, 3).map(([keyword]) => keyword);
    tags.push(...topKeywords);
  }

  if (results.images?.length > 0) {
    tags.push('Images');
  }

  if (results.tables?.length > 0) {
    tags.push('Tables');
  }

  return tags.slice(0, 5); // Limit to 5 tags
};

const CompletedPapers = () => {
  const [completedPapers, setCompletedPapers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [selectedPapers, setSelectedPapers] = useState([]);

  // Fetch real completed papers from API
  useEffect(() => {
    const fetchCompletedPapers = async () => {
      try {
        const tasksResponse = await apiService.getAllTasks();
        const completedTasks = tasksResponse.tasks.filter(task =>
          task.status === 'completed'
        );

        // Get detailed results for each completed task
        const detailedTasks = await Promise.all(
          completedTasks.map(async (task) => {
            try {
              const results = await apiService.getResults(task.task_id);
              return {
                id: task.task_id,
                title: results.metadata?.title || 'Unknown Document',
                authors: results.metadata?.author ? [results.metadata.author] : ['Unknown'],
                uploadDate: task.started_at?.split('T')[0] || new Date().toISOString().split('T')[0],
                completedDate: new Date().toISOString(),
                processingTime: 'Unknown',
                fileSize: 'Unknown',
                models: 0, // Not applicable for document analysis
                apis: 0,   // Not applicable for document analysis
                downloads: 0,
                views: Math.floor(Math.random() * 100),
                tags: extractTags(results),
                accuracy: 95.0, // Default accuracy for successful processing
                performance: 'Excellent',
                generatedAssets: {
                  keywords: results.insights?.tfidf_keywords?.length || 0,
                  entities: results.insights?.named_entities?.length || 0,
                  images: results.images?.length || 0,
                  tables: results.tables?.length || 0,
                  analysis: ['document_analysis.json', 'extracted_content.txt'],
                  metadata: ['document_metadata.json']
                },
                results: results,
                taskId: task.task_id
              };
            } catch (error) {
              console.error(`Error fetching results for task ${task.task_id}:`, error);
              return null;
            }
          })
        );

        setCompletedPapers(detailedTasks.filter(task => task !== null));
      } catch (error) {
        console.error('Error fetching completed papers:', error);
        setCompletedPapers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCompletedPapers();
  }, []);

  // Filter and sort papers
  const filteredPapers = completedPapers
    .filter(paper => {
      const matchesSearch = paper.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           paper.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
                           paper.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.completedDate) - new Date(a.completedDate);
        case 'title':
          return a.title.localeCompare(b.title);
        case 'accuracy':
          return b.accuracy - a.accuracy;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'models':
          return b.models - a.models;
        default:
          return 0;
      }
    });

  const getPerformanceColor = (performance) => {
    switch (performance) {
      case 'Outstanding':
        return 'bg-green-100 text-green-800';
      case 'Excellent':
        return 'bg-blue-100 text-blue-800';
      case 'Very Good':
        return 'bg-purple-100 text-purple-800';
      case 'Good':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSelectPaper = (paperId) => {
    setSelectedPapers(prev =>
      prev.includes(paperId)
        ? prev.filter(id => id !== paperId)
        : [...prev, paperId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPapers.length === filteredPapers.length) {
      setSelectedPapers([]);
    } else {
      setSelectedPapers(filteredPapers.map(paper => paper.id));
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Completed Papers</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Successfully processed papers with generated models and APIs
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/papers"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                All Papers
              </Link>
              <Link
                to="/dashboard/upload"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Upload New
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Total Completed',
              value: completedPapers.length,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              )
            },
            {
              label: 'Total Keywords',
              value: completedPapers.reduce((sum, paper) => sum + (paper.generatedAssets?.keywords || 0), 0),
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              )
            },
            {
              label: 'Total Entities',
              value: completedPapers.reduce((sum, paper) => sum + (paper.generatedAssets?.entities || 0), 0),
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              )
            },
            {
              label: 'Total Images',
              value: completedPapers.reduce((sum, paper) => sum + (paper.generatedAssets?.images || 0), 0),
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search completed papers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Sort */}
            <div className="flex space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Completion Date</option>
                <option value="title">Sort by Title</option>
                <option value="accuracy">Sort by Accuracy</option>
                <option value="downloads">Sort by Downloads</option>
                <option value="models">Sort by Models Count</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Completed Papers List */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {loading ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading completed papers...</p>
            </div>
          ) : filteredPapers.length === 0 ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No completed papers found</h3>
              <p className="mt-2 text-primary">
                {searchQuery
                  ? 'Try adjusting your search criteria'
                  : 'Upload and process papers to see them here when completed'
                }
              </p>
              {!searchQuery && (
                <Link
                  to="/upload-paper"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Upload Paper
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Bulk Actions */}
              {selectedPapers.length > 0 && (
                <motion.div
                  className="bg-blue-50 border border-blue-200 rounded-xl p-4"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-800">
                      {selectedPapers.length} paper{selectedPapers.length > 1 ? 's' : ''} selected
                    </span>
                    <div className="flex space-x-2">
                      <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        Export Selected
                      </button>
                      <button className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                        Download Models
                      </button>
                      <button className="px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                        Delete Selected
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}

              <AnimatePresence>
                {filteredPapers.map((paper, index) => (
                  <motion.div
                    key={paper.id}
                    className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
                    variants={itemVariants}
                    initial="hidden"
                    animate="visible"
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="p-4 lg:p-6">
                      {/* Paper Header */}
                      <div className="flex items-start space-x-4 mb-6">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded mt-1"
                          checked={selectedPapers.includes(paper.id)}
                          onChange={() => handleSelectPaper(paper.id)}
                        />

                        <div className="flex-1">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                            <div className="flex-1">
                              <h3 className="text-lg lg:text-xl font-semibold text-accent hover:text-highlight transition-colors duration-200">
                                <Link to={`/dashboard/papers/${paper.id}`}>
                                  {paper.title}
                                </Link>
                              </h3>
                              <p className="text-sm text-primary mt-1">
                                by {paper.authors.join(', ')}
                              </p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {paper.tags.map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>

                            <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(paper.performance)}`}>
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                                {paper.performance}
                              </div>
                              <div className="text-xs text-primary">
                                Completed: {new Date(paper.completedDate).toLocaleDateString()}
                              </div>
                              <div className="text-xs font-medium text-highlight">
                                {paper.accuracy}% Accuracy
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Generated Assets Summary */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div className="bg-blue-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-blue-600 font-medium">Keywords</p>
                              <p className="text-lg font-bold text-blue-800">{paper.generatedAssets.keywords}</p>
                            </div>
                            <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-green-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-green-600 font-medium">Entities</p>
                              <p className="text-lg font-bold text-green-800">{paper.generatedAssets.entities}</p>
                            </div>
                            <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-purple-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-purple-600 font-medium">Images</p>
                              <p className="text-lg font-bold text-purple-800">{paper.generatedAssets.images}</p>
                            </div>
                            <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        </div>

                        <div className="bg-yellow-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-xs text-yellow-600 font-medium">Tables</p>
                              <p className="text-lg font-bold text-yellow-800">{paper.generatedAssets.tables}</p>
                            </div>
                            <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h12a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1z" />
                            </svg>
                          </div>
                        </div>
                      </div>

                      {/* Generated Assets Details */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-accent mb-3">Generated Assets</h4>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {/* Analysis Files */}
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h5 className="text-xs font-medium text-accent mb-2 flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              Analysis Files ({paper.generatedAssets.analysis?.length || 0})
                            </h5>
                            <div className="space-y-1">
                              {paper.generatedAssets.analysis?.slice(0, 3).map((file, idx) => (
                                <div key={idx} className="text-xs text-primary bg-white rounded px-2 py-1">
                                  {file}
                                </div>
                              )) || <div className="text-xs text-gray-500">No analysis files</div>}
                              {(paper.generatedAssets.analysis?.length || 0) > 3 && (
                                <div className="text-xs text-highlight">
                                  +{paper.generatedAssets.analysis.length - 3} more
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Metadata */}
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h5 className="text-xs font-medium text-accent mb-2 flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              Metadata Files ({paper.generatedAssets.metadata?.length || 0})
                            </h5>
                            <div className="space-y-1">
                              {paper.generatedAssets.metadata?.map((file, idx) => (
                                <div key={idx} className="text-xs text-primary bg-white rounded px-2 py-1">
                                  {file}
                                </div>
                              )) || <div className="text-xs text-gray-500">No metadata files</div>}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Paper Details */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm mb-6">
                        <div>
                          <span className="text-primary">File Size:</span>
                          <span className="ml-1 font-medium text-accent">{paper.fileSize}</span>
                        </div>
                        <div>
                          <span className="text-primary">Processing Time:</span>
                          <span className="ml-1 font-medium text-accent">{paper.processingTime}</span>
                        </div>
                        <div>
                          <span className="text-primary">Uploaded:</span>
                          <span className="ml-1 font-medium text-accent">
                            {new Date(paper.uploadDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div>
                          <span className="text-primary">Completed:</span>
                          <span className="ml-1 font-medium text-accent">
                            {new Date(paper.completedDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                        <Link
                          to={`/dashboard/results/${paper.taskId}`}
                          className="inline-flex items-center px-3 py-1 text-xs font-medium bg-highlight text-white rounded hover:bg-opacity-90 transition-colors duration-200"
                        >
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View Results
                        </Link>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          Download Analysis
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Export Data
                        </button>
                        <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          Delete
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default CompletedPapers;