# 🌐 SciML Platform - Web Application

A beautiful, modern web interface for scientific literature analysis with advanced AI-powered processing capabilities.

## ✨ Features

### 🎨 **Beautiful UI/UX Design**
- **Clean White & Navy Blue Theme**: Professional, modern design
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile
- **Intuitive Interface**: Simple upload or URL input with real-time progress
- **Interactive Results**: Structured data presentation with visual cards

### 🚀 **Core Functionality**
- **PDF Upload**: Drag & drop or click to upload (max 50MB)
- **URL Analysis**: Direct ArXiv links or PDF URLs
- **Real-time Processing**: Live progress updates with status messages
- **Comprehensive Results**: Document metadata, text analysis, visual content extraction

### 🔧 **Technical Features**
- **Asynchronous Processing**: Background document analysis
- **Web-Suitable OCR**: EasyOCR integration for containerized deployment
- **RESTful API**: Clean API endpoints for integration
- **Error Handling**: Robust error recovery and user feedback

## 🚀 Quick Start

### Local Development
```bash
# Install dependencies
pip install -r requirements_web.txt

# Download spaCy model
python -m spacy download en_core_web_sm

# Run the application
python app.py
```

Access the application at: **http://localhost:5000**

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build manually
docker build -t sciml-platform .
docker run -p 5000:5000 sciml-platform
```

## 📱 User Interface

### Main Features
1. **Upload Tab**: Drag & drop PDF files or click to browse
2. **URL Tab**: Enter ArXiv URLs or direct PDF links
3. **Progress Tracking**: Real-time analysis progress with status updates
4. **Results Dashboard**: Comprehensive analysis results in beautiful cards

### Results Display
- **Document Information**: Title, author, pages, creation date
- **Text Analysis**: Word count, readability scores, sentence analysis
- **Visual Content**: Images, tables, equations, and sections count
- **Keywords & Entities**: Top keywords with TF-IDF scores and named entities
- **Scientific Terms**: Categorized models, metrics, datasets, and techniques

## 🔧 API Endpoints

### Upload File
```http
POST /api/upload
Content-Type: multipart/form-data

Response: {"task_id": "uuid", "status": "started"}
```

### Analyze URL
```http
POST /api/analyze-url
Content-Type: application/json
{"url": "https://arxiv.org/abs/2505.17303"}

Response: {"task_id": "uuid", "status": "started"}
```

### Check Status
```http
GET /api/status/{task_id}

Response: {
  "status": "processing|completed|error",
  "progress": 75,
  "message": "Extracting content...",
  "results": {...}  // Only when completed
}
```

### Get Results
```http
GET /api/results/{task_id}

Response: {
  "metadata": {...},
  "insights": {...},
  "statistics": {...},
  "processing_timestamp": "..."
}
```

### Health Check
```http
GET /health

Response: {
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00",
  "version": "1.0.0"
}
```

## 🎨 Design System

### Color Palette
- **Primary Navy**: `#003f5c` - Headers, primary buttons
- **Dark Navy**: `#2f4b7c` - Hover states, secondary elements
- **Accent Blue**: `#00b5ad` - Icons, progress bars, highlights
- **Light Gray**: `#f8fafc` - Background, subtle elements
- **White**: `#ffffff` - Cards, main content areas

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700
- **Responsive**: Scales appropriately on all devices

### Components
- **Cards**: Clean white cards with subtle shadows
- **Buttons**: Navy blue with hover effects
- **Progress Bars**: Gradient blue progress indicators
- **Tags**: Rounded tags for keywords and entities

## 🐳 Production Deployment

### Environment Variables
```bash
FLASK_ENV=production
PYTHONUNBUFFERED=1
```

### Docker Production
```bash
# Build for production
docker build -t sciml-platform:latest .

# Run with production settings
docker run -d \
  --name sciml-platform \
  -p 5000:5000 \
  -v $(pwd)/output:/app/output \
  -e FLASK_ENV=production \
  sciml-platform:latest
```

### Cloud Deployment
The application is ready for deployment on:
- **AWS ECS/Fargate**
- **Google Cloud Run**
- **Azure Container Instances**
- **Heroku**
- **DigitalOcean App Platform**

## 📊 Performance

### Optimization Features
- **Lazy OCR Loading**: EasyOCR initialized only when needed
- **Background Processing**: Non-blocking document analysis
- **Memory Management**: Automatic cleanup after processing
- **Caching**: Results cached during session

### Typical Performance
- **Small PDFs (1-5 pages)**: 30-60 seconds
- **Medium PDFs (5-15 pages)**: 1-3 minutes
- **Large PDFs (15+ pages)**: 3-5 minutes
- **URL Downloads**: +10-30 seconds for download

## 🔒 Security

### Input Validation
- File type validation (PDF only)
- File size limits (50MB max)
- URL validation and sanitization
- Secure filename handling

### Best Practices
- No persistent file storage
- Temporary file cleanup
- Error message sanitization
- CORS protection available

## 🛠️ Development

### Project Structure
```
AI_tests/
├── app.py                 # Main Flask application
├── part_1.py             # SciML processing engine
├── templates/
│   └── index.html        # Main UI template
├── static/
│   └── js/
│       └── app.js        # Frontend JavaScript
├── requirements_web.txt   # Web dependencies
├── Dockerfile            # Container configuration
└── docker-compose.yml    # Orchestration
```

### Adding Features
1. **Backend**: Extend `app.py` with new endpoints
2. **Frontend**: Update `templates/index.html` and `static/js/app.js`
3. **Processing**: Enhance `part_1.py` for new analysis features

## 🎯 Usage Examples

### Upload PDF
1. Click "Upload PDF" tab
2. Drag & drop a PDF file or click to browse
3. Watch real-time progress
4. View comprehensive results

### Analyze URL
1. Click "Analyze URL" tab
2. Enter ArXiv URL: `https://arxiv.org/abs/2505.17303`
3. Click "Analyze" button
4. Monitor progress and view results

## 🚀 **SciML Platform Web Application is Ready!**

The web application provides a beautiful, professional interface for scientific literature analysis with:
- ✅ Modern UI/UX with navy blue and white theme
- ✅ Real-time processing with progress tracking
- ✅ Comprehensive results display
- ✅ Mobile-responsive design
- ✅ Docker-ready for easy deployment
- ✅ RESTful API for integration

**Access your SciML Platform at: http://localhost:5000** 🎉
