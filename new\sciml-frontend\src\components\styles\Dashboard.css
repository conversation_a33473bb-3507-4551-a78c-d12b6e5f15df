/* Dashboard.css - Styles for the Dashboard component */

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Card hover effect */
.dashboard-card {
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Progress bar animation */
@keyframes progress-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 181, 173, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0);
  }
}

.progress-bar-animate {
  animation: progress-pulse 2s infinite;
}

/* Chart bar hover effect */
.chart-bar {
  transition: all 0.3s ease;
}

.chart-bar:hover {
  transform: scaleY(1.05);
}

/* Table row hover effect */
.table-row-hover {
  transition: background-color 0.2s ease;
}

.table-row-hover:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Status badge glow effect */
.status-badge {
  position: relative;
}

.status-badge::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 9999px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.status-badge:hover::after {
  opacity: 0.5;
}

.status-badge.processed::after {
  background: rgba(16, 185, 129, 0.5);
}

.status-badge.processing::after {
  background: rgba(59, 130, 246, 0.5);
}

.status-badge.failed::after {
  background: rgba(239, 68, 68, 0.5);
}

.status-badge.pending::after {
  background: rgba(245, 158, 11, 0.5);
}

/* Tab hover effect */
.tab-hover {
  position: relative;
  overflow: hidden;
}

.tab-hover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--highlight-color, #00b5ad);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.tab-hover:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Button glow effect */
.btn-glow:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.6);
}
