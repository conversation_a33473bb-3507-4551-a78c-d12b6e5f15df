import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const ProcessingPapers = () => {
  const [processingPapers, setProcessingPapers] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data for processing papers
  useEffect(() => {
    const mockProcessingPapers = [
      {
        id: 2,
        title: 'Quantum Computing Applications in Drug Discovery',
        authors: ['Dr. <PERSON>', 'Dr. <PERSON>'],
        uploadDate: '2024-01-14T10:30:00Z',
        startTime: '2024-01-14T10:35:00Z',
        estimatedCompletion: '2024-01-14T12:45:00Z',
        progress: 65,
        currentStage: 'Model Generation',
        stages: [
          { name: 'File Processing', status: 'completed', duration: '5 min' },
          { name: 'Text Extraction', status: 'completed', duration: '8 min' },
          { name: 'Content Analysis', status: 'completed', duration: '15 min' },
          { name: 'Model Generation', status: 'processing', duration: '45 min (est.)' },
          { name: 'API Creation', status: 'pending', duration: '20 min (est.)' },
          { name: 'Validation', status: 'pending', duration: '10 min (est.)' }
        ],
        fileSize: '6.8 MB',
        tags: ['Quantum Computing', 'Drug Discovery', 'Molecular Dynamics'],
        priority: 'high'
      },
      {
        id: 6,
        title: 'Advanced Neural Architecture Search for Climate Modeling',
        authors: ['Prof. Maria Santos', 'Dr. Kevin Liu'],
        uploadDate: '2024-01-14T14:20:00Z',
        startTime: '2024-01-14T14:25:00Z',
        estimatedCompletion: '2024-01-14T17:30:00Z',
        progress: 25,
        currentStage: 'Content Analysis',
        stages: [
          { name: 'File Processing', status: 'completed', duration: '3 min' },
          { name: 'Text Extraction', status: 'completed', duration: '12 min' },
          { name: 'Content Analysis', status: 'processing', duration: '25 min (est.)' },
          { name: 'Model Generation', status: 'pending', duration: '60 min (est.)' },
          { name: 'API Creation', status: 'pending', duration: '25 min (est.)' },
          { name: 'Validation', status: 'pending', duration: '15 min (est.)' }
        ],
        fileSize: '15.2 MB',
        tags: ['Neural Architecture', 'Climate Modeling', 'AutoML'],
        priority: 'medium'
      },
      {
        id: 7,
        title: 'Reinforcement Learning for Autonomous Vehicle Navigation',
        authors: ['Dr. Alex Chen', 'Prof. Sarah Johnson'],
        uploadDate: '2024-01-14T16:45:00Z',
        startTime: '2024-01-14T16:50:00Z',
        estimatedCompletion: '2024-01-14T19:20:00Z',
        progress: 10,
        currentStage: 'Text Extraction',
        stages: [
          { name: 'File Processing', status: 'completed', duration: '4 min' },
          { name: 'Text Extraction', status: 'processing', duration: '18 min (est.)' },
          { name: 'Content Analysis', status: 'pending', duration: '30 min (est.)' },
          { name: 'Model Generation', status: 'pending', duration: '50 min (est.)' },
          { name: 'API Creation', status: 'pending', duration: '22 min (est.)' },
          { name: 'Validation', status: 'pending', duration: '12 min (est.)' }
        ],
        fileSize: '9.4 MB',
        tags: ['Reinforcement Learning', 'Autonomous Vehicles', 'Navigation'],
        priority: 'low'
      }
    ];

    setTimeout(() => {
      setProcessingPapers(mockProcessingPapers);
      setLoading(false);
    }, 1000);

    // Simulate progress updates
    const interval = setInterval(() => {
      setProcessingPapers(prev => prev.map(paper => ({
        ...paper,
        progress: Math.min(paper.progress + Math.random() * 5, 95)
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getTimeRemaining = (estimatedCompletion) => {
    const now = new Date();
    const completion = new Date(estimatedCompletion);
    const diff = completion - now;

    if (diff <= 0) return 'Completing...';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    }
    return `${minutes}m remaining`;
  };

  const getElapsedTime = (startTime) => {
    const now = new Date();
    const start = new Date(startTime);
    const diff = now - start;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m elapsed`;
    }
    return `${minutes}m elapsed`;
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStageIcon = (status) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'processing':
        return (
          <svg className="w-4 h-4 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Processing Papers</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Monitor the real-time processing status of your uploaded papers
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/papers"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                All Papers
              </Link>
              <Link
                to="/dashboard/upload"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Upload New
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
            variants={itemVariants}
          >
            <div className="flex items-center">
              <div className="w-3 h-3 lg:w-4 lg:h-4 rounded-full bg-blue-500 mr-3"></div>
              <div>
                <p className="text-xs lg:text-sm text-primary">Currently Processing</p>
                <p className="text-lg lg:text-2xl font-bold text-accent">{processingPapers.length}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
            variants={itemVariants}
          >
            <div className="flex items-center">
              <div className="w-3 h-3 lg:w-4 lg:h-4 rounded-full bg-green-500 mr-3"></div>
              <div>
                <p className="text-xs lg:text-sm text-primary">Avg. Progress</p>
                <p className="text-lg lg:text-2xl font-bold text-accent">
                  {processingPapers.length > 0
                    ? Math.round(processingPapers.reduce((sum, paper) => sum + paper.progress, 0) / processingPapers.length)
                    : 0}%
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
            variants={itemVariants}
          >
            <div className="flex items-center">
              <div className="w-3 h-3 lg:w-4 lg:h-4 rounded-full bg-yellow-500 mr-3"></div>
              <div>
                <p className="text-xs lg:text-sm text-primary">Queue Position</p>
                <p className="text-lg lg:text-2xl font-bold text-accent">
                  {processingPapers.filter(p => p.priority === 'high').length} High Priority
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Processing Papers List */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {loading ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading processing papers...</p>
            </div>
          ) : processingPapers.length === 0 ? (
            <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No papers currently processing</h3>
              <p className="mt-2 text-primary">Upload a new paper to see it appear here during processing</p>
              <Link
                to="/dashboard/upload"
                className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
              >
                Upload Paper
              </Link>
            </div>
          ) : (
            <AnimatePresence>
              {processingPapers.map((paper, index) => (
                <motion.div
                  key={paper.id}
                  className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="p-4 lg:p-6">
                    {/* Paper Header */}
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-lg lg:text-xl font-semibold text-accent">
                              {paper.title}
                            </h3>
                            <p className="text-sm text-primary mt-1">
                              by {paper.authors.join(', ')}
                            </p>
                            <div className="flex flex-wrap gap-2 mt-2">
                              {paper.tags.map((tag, tagIndex) => (
                                <span
                                  key={tagIndex}
                                  className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(paper.priority)}`}>
                            {paper.priority.toUpperCase()} PRIORITY
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Progress Overview */}
                    <div className="mb-6">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                        <div>
                          <p className="text-sm font-medium text-accent">
                            Current Stage: {paper.currentStage}
                          </p>
                          <p className="text-xs text-primary">
                            {getElapsedTime(paper.startTime)} • {getTimeRemaining(paper.estimatedCompletion)}
                          </p>
                        </div>
                        <div className="mt-2 sm:mt-0">
                          <span className="text-lg font-bold text-highlight">{Math.round(paper.progress)}%</span>
                          <span className="text-sm text-primary ml-1">complete</span>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          className="bg-gradient-to-r from-highlight to-accent h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${paper.progress}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>

                    {/* Processing Stages */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-accent mb-3">Processing Stages</h4>
                      <div className="space-y-3">
                        {paper.stages.map((stage, stageIndex) => (
                          <div
                            key={stageIndex}
                            className={`flex items-center justify-between p-3 rounded-lg ${
                              stage.status === 'processing'
                                ? 'bg-blue-50 border border-blue-200'
                                : stage.status === 'completed'
                                ? 'bg-green-50 border border-green-200'
                                : 'bg-gray-50 border border-gray-200'
                            }`}
                          >
                            <div className="flex items-center">
                              {getStageIcon(stage.status)}
                              <span className={`ml-3 text-sm font-medium ${
                                stage.status === 'processing'
                                  ? 'text-blue-700'
                                  : stage.status === 'completed'
                                  ? 'text-green-700'
                                  : 'text-gray-500'
                              }`}>
                                {stage.name}
                              </span>
                            </div>
                            <span className={`text-xs ${
                              stage.status === 'processing'
                                ? 'text-blue-600'
                                : stage.status === 'completed'
                                ? 'text-green-600'
                                : 'text-gray-500'
                            }`}>
                              {stage.duration}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Paper Details */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm mb-6">
                      <div>
                        <span className="text-primary">File Size:</span>
                        <span className="ml-1 font-medium text-accent">{paper.fileSize}</span>
                      </div>
                      <div>
                        <span className="text-primary">Uploaded:</span>
                        <span className="ml-1 font-medium text-accent">
                          {new Date(paper.uploadDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-primary">Started:</span>
                        <span className="ml-1 font-medium text-accent">
                          {new Date(paper.startTime).toLocaleTimeString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-primary">ETA:</span>
                        <span className="ml-1 font-medium text-accent">
                          {new Date(paper.estimatedCompletion).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                      <Link
                        to={`/dashboard/papers/${paper.id}`}
                        className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                      >
                        View Details
                      </Link>
                      <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                        View Logs
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 hover:text-yellow-800 transition-colors duration-200">
                        Pause Processing
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                        Cancel
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default ProcessingPapers;