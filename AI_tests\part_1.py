#!/usr/bin/env python3
"""
SciML Platform - Full System Pipeline
A comprehensive scientific literature analysis platform with advanced features:
- Document ingestion (PDF upload/URL)
- NLP & insight extraction
- Visual content extraction (tables, figures)
- Dataset building capabilities
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings("ignore")

# Core libraries
import requests
import fitz  # PyMuPDF
import pdfplumber
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
import cv2
from PIL import Image
import io
import base64

# NLP libraries
import spacy
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
import re
from collections import Counter, defaultdict

# ML libraries
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from textstat import flesch_reading_ease, flesch_kincaid_grade

# Image processing and OCR setup (Web-suitable)
OCR_AVAILABLE = False
OCR_ENGINE = None

# Try EasyOCR first (best for web deployment - no external dependencies)
try:
    import easyocr
    OCR_ENGINE = 'easyocr'
    OCR_AVAILABLE = True
    # Don't print during import to avoid hanging
except ImportError:
    # Fallback to pytesseract if available
    try:
        import pytesseract
        try:
            pytesseract.get_tesseract_version()
            OCR_ENGINE = 'pytesseract'
            OCR_AVAILABLE = True
        except Exception:
            pass
    except ImportError:
        pass

# Initialize EasyOCR reader lazily to avoid startup delays
EASYOCR_READER = None

def get_easyocr_reader():
    """Get EasyOCR reader with lazy initialization"""
    global EASYOCR_READER
    if EASYOCR_READER is None and OCR_ENGINE == 'easyocr':
        try:
            import easyocr
            print("🔄 Initializing EasyOCR (this may take a moment on first run)...")
            EASYOCR_READER = easyocr.Reader(['en'], gpu=False)  # CPU mode for better compatibility
            print("✅ EasyOCR initialized successfully!")
        except Exception as e:
            print(f"⚠️  EasyOCR initialization failed: {e}")
            return None
    return EASYOCR_READER

# Download required NLTK data with better error handling
def download_nltk_data():
    """Download required NLTK data with proper error handling"""
    required_data = [
        ('tokenizers/punkt', 'punkt'),
        ('tokenizers/punkt_tab', 'punkt_tab'),
        ('corpora/stopwords', 'stopwords'),
        ('corpora/wordnet', 'wordnet'),
        ('taggers/averaged_perceptron_tagger', 'averaged_perceptron_tagger')
    ]

    for data_path, data_name in required_data:
        try:
            nltk.data.find(data_path)
        except LookupError:
            try:
                nltk.download(data_name, quiet=True)
            except Exception as e:
                print(f"Warning: Could not download {data_name}: {e}")

download_nltk_data()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sciml_platform.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VisualContentExtractor:
    """Enhanced visual content extraction for tables, figures, and graphs"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def extract_images_from_pdf(self, file_path: str) -> List[Dict]:
        """Extract and analyze images from PDF"""
        images = []
        try:
            with fitz.open(file_path) as doc:
                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    image_list = page.get_images()

                    for img_index, img in enumerate(image_list):
                        try:
                            xref = img[0]
                            pix = fitz.Pixmap(doc, xref)

                            if pix.n - pix.alpha < 4:  # GRAY or RGB
                                img_data = pix.tobytes("png")

                                # Convert to PIL Image for analysis
                                pil_image = Image.open(io.BytesIO(img_data))

                                # Analyze image type (figure, chart, diagram, etc.)
                                image_type = self._classify_image_type(pil_image)

                                images.append({
                                    'page': page_num + 1,
                                    'index': img_index,
                                    'width': pix.width,
                                    'height': pix.height,
                                    'colorspace': pix.colorspace.name if pix.colorspace else 'Unknown',
                                    'type': image_type,
                                    'data': base64.b64encode(img_data).decode(),
                                    'ocr_text': self._extract_text_from_image(pil_image) if OCR_AVAILABLE else None
                                })
                            pix = None
                        except Exception as e:
                            self.logger.warning(f"Could not process image {img_index} from page {page_num + 1}: {e}")
        except Exception as e:
            self.logger.error(f"Image extraction failed: {e}")

        return images

    def _classify_image_type(self, image: Image.Image) -> str:
        """Classify image type based on visual characteristics"""
        # Convert to numpy array for analysis
        img_array = np.array(image.convert('RGB'))

        # Simple heuristics for image classification
        height, width = img_array.shape[:2]
        aspect_ratio = width / height

        # Check for chart-like characteristics
        if aspect_ratio > 1.2 and height < 600:  # Likely a chart or graph
            return "chart_graph"
        elif aspect_ratio < 0.8:  # Tall image, might be a diagram
            return "diagram"
        elif width > 400 and height > 300:  # Large image, likely a figure
            return "figure"
        else:
            return "unknown"

    def _extract_text_from_image(self, image: Image.Image) -> Optional[str]:
        """Extract text from image using available OCR engine"""
        if not OCR_AVAILABLE:
            return None

        try:
            if OCR_ENGINE == 'easyocr':
                # Get EasyOCR reader with lazy initialization
                reader = get_easyocr_reader()
                if reader is not None:
                    # Convert PIL Image to numpy array for EasyOCR
                    img_array = np.array(image)

                    # EasyOCR returns list of [bbox, text, confidence]
                    results = reader.readtext(img_array)

                    # Extract text with confidence > 0.5
                    extracted_texts = []
                    for (bbox, text, confidence) in results:
                        if confidence > 0.5 and text.strip():
                            extracted_texts.append(text.strip())

                    if extracted_texts:
                        return ' '.join(extracted_texts)
                    else:
                        return None
                else:
                    return None

            elif OCR_ENGINE == 'pytesseract':
                import pytesseract
                text = pytesseract.image_to_string(image)
                return text.strip() if text.strip() else None

            else:
                self.logger.warning("No valid OCR engine available")
                return None

        except Exception as e:
            self.logger.warning(f"OCR extraction failed with {OCR_ENGINE}: {e}")
            return None

class DatasetBuilder:
    """Build structured datasets from extracted content"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def build_dataset(self, parsed_data: Dict) -> Dict:
        """Build structured dataset from parsed content"""
        dataset = {
            'document_info': self._structure_document_info(parsed_data),
            'content_structure': self._structure_content(parsed_data),
            'extracted_data': self._structure_extracted_data(parsed_data),
            'metadata': self._normalize_metadata(parsed_data.get('metadata', {}))
        }

        return dataset

    def _structure_document_info(self, data: Dict) -> Dict:
        """Structure basic document information"""
        return {
            'title': data.get('metadata', {}).get('title', ''),
            'author': data.get('metadata', {}).get('author', ''),
            'page_count': data.get('metadata', {}).get('page_count', 0),
            'creation_date': data.get('metadata', {}).get('creationDate', ''),
            'word_count': len(data.get('text', '').split()),
            'has_images': len(data.get('images', [])) > 0,
            'has_tables': len(data.get('tables', [])) > 0,
            'has_equations': len(data.get('equations', [])) > 0
        }

    def _structure_content(self, data: Dict) -> Dict:
        """Structure content by type"""
        return {
            'text_content': {
                'full_text': data.get('text', ''),
                'sections': data.get('sections', {}),
                'equations': data.get('equations', [])
            },
            'visual_content': {
                'images': data.get('images', []),
                'tables': data.get('tables', [])
            }
        }

    def _structure_extracted_data(self, data: Dict) -> Dict:
        """Structure extracted insights and data"""
        insights = data.get('insights', {})
        return {
            'keywords': insights.get('tfidf_keywords', []),
            'entities': insights.get('named_entities', []),
            'scientific_terms': insights.get('scientific_terms', {}),
            'statistics': insights.get('statistics', {})
        }

    def _normalize_metadata(self, metadata: Dict) -> Dict:
        """Normalize and clean metadata"""
        normalized = {}
        for key, value in metadata.items():
            if value and str(value).strip():
                normalized[key] = str(value).strip()
        return normalized

    def export_to_csv(self, dataset: Dict, output_path: str) -> bool:
        """Export dataset to CSV format"""
        try:
            # Create a flattened structure for CSV
            rows = []

            # Document info row
            doc_info = dataset['document_info']
            content = dataset['content_structure']
            extracted = dataset['extracted_data']

            # Main document row
            main_row = {
                'type': 'document',
                'title': doc_info.get('title', ''),
                'author': doc_info.get('author', ''),
                'page_count': doc_info.get('page_count', 0),
                'word_count': doc_info.get('word_count', 0),
                'has_images': doc_info.get('has_images', False),
                'has_tables': doc_info.get('has_tables', False),
                'has_equations': doc_info.get('has_equations', False)
            }
            rows.append(main_row)

            # Keywords rows
            for keyword, score in extracted.get('keywords', [])[:20]:
                rows.append({
                    'type': 'keyword',
                    'content': keyword,
                    'score': score,
                    'category': 'tfidf'
                })

            # Entities rows
            for entity in extracted.get('entities', [])[:20]:
                rows.append({
                    'type': 'entity',
                    'content': entity.get('text', ''),
                    'category': entity.get('label', ''),
                    'description': entity.get('description', '')
                })

            # Save to CSV
            df = pd.DataFrame(rows)
            df.to_csv(output_path, index=False)
            self.logger.info(f"Dataset exported to CSV: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"CSV export failed: {e}")
            return False

class DocumentParser:
    """Enhanced PDF parser with comprehensive extraction capabilities"""

    def __init__(self):
        self.supported_formats = ['.pdf']
        self.visual_extractor = VisualContentExtractor()
        self.dataset_builder = DatasetBuilder()

    def extract_from_pdf(self, file_path: str) -> Dict:
        """Extract comprehensive information from PDF"""
        logger.info(f"Parsing PDF: {file_path}")

        result = {
            'text': '',
            'metadata': {},
            'sections': {},
            'equations': [],
            'images': [],
            'tables': [],
            'visual_content': {}
        }

        try:
            # Method 1: PyMuPDF for metadata and enhanced text extraction
            with fitz.open(file_path) as doc:
                result['metadata'] = {
                    'title': doc.metadata.get('title', ''),
                    'author': doc.metadata.get('author', ''),
                    'subject': doc.metadata.get('subject', ''),
                    'creator': doc.metadata.get('creator', ''),
                    'producer': doc.metadata.get('producer', ''),
                    'created': doc.metadata.get('creationDate', ''),
                    'modified': doc.metadata.get('modDate', ''),
                    'pages': doc.page_count
                }

                # Extract text with better formatting
                full_text = []
                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    page_text = page.get_text()
                    full_text.append(page_text)

                    # Extract equations from each page
                    equations = self._extract_equations(page_text)
                    if equations:
                        result['equations'].extend(equations)

                result['text'] = '\n'.join(full_text)

            # Extract visual content using enhanced extractor
            result['images'] = self.visual_extractor.extract_images_from_pdf(file_path)

            # Method 2: pdfplumber for enhanced table extraction
            try:
                with pdfplumber.open(file_path) as pdf:
                    for page_num, page in enumerate(pdf.pages):
                        tables = page.extract_tables()
                        for table_index, table in enumerate(tables):
                            if table and len(table) > 1:  # Ensure it's a valid table
                                # Clean and structure table data
                                cleaned_table = self._clean_table_data(table)
                                result['tables'].append({
                                    'page': page_num + 1,
                                    'index': table_index,
                                    'rows': len(cleaned_table),
                                    'columns': len(cleaned_table[0]) if cleaned_table else 0,
                                    'data': cleaned_table,
                                    'headers': cleaned_table[0] if cleaned_table else [],
                                    'has_numeric_data': self._has_numeric_data(cleaned_table)
                                })
            except Exception as e:
                logger.warning(f"Table extraction failed: {e}")

        except Exception as e:
            logger.error(f"Error parsing PDF: {e}")
            return None

        return result

    def _extract_equations(self, text: str) -> List[str]:
        """Extract mathematical equations from text"""
        # Enhanced pattern for common equation indicators
        equation_patterns = [
            r'equation\s*\(\d+\)',
            r'eq\.\s*\(\d+\)',
            r'\$[^$]+\$',  # LaTeX inline math
            r'\\\([^)]+\\\)',  # LaTeX display math
            r'\\\[[^\]]+\\\]',  # LaTeX display math
            r'[A-Z]\s*[=]\s*[^.!?]*[0-9+\-*/()^]+',  # Simple equations like E = mc²
            r'[a-zA-Z]\s*[=]\s*[^.!?]*[0-9+\-*/()^]+'  # Variable equations
        ]

        equations = []
        for pattern in equation_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            equations.extend(matches)

        # Remove duplicates and clean
        unique_equations = []
        for eq in equations:
            cleaned = eq.strip()
            if cleaned and len(cleaned) > 3 and cleaned not in unique_equations:
                unique_equations.append(cleaned)

        return unique_equations[:20]  # Limit to 20 equations

    def _clean_table_data(self, table: List[List]) -> List[List]:
        """Clean and normalize table data"""
        cleaned = []
        for row in table:
            cleaned_row = []
            for cell in row:
                if cell is not None:
                    # Clean cell content
                    cleaned_cell = str(cell).strip().replace('\n', ' ').replace('\r', '')
                    # Handle missing values
                    if not cleaned_cell or cleaned_cell.lower() in ['', 'nan', 'null', 'none']:
                        cleaned_cell = ''
                    cleaned_row.append(cleaned_cell)
                else:
                    cleaned_row.append('')
            if any(cleaned_row):  # Only add non-empty rows
                cleaned.append(cleaned_row)
        return cleaned

    def _has_numeric_data(self, table: List[List]) -> bool:
        """Check if table contains numeric data"""
        if len(table) < 2:
            return False

        numeric_count = 0
        total_cells = 0

        for row in table[1:]:  # Skip header
            for cell in row:
                if cell and str(cell).strip():
                    total_cells += 1
                    try:
                        # Try to convert to float after cleaning
                        cleaned_cell = str(cell).replace(',', '').replace('%', '').replace('$', '').strip()
                        float(cleaned_cell)
                        numeric_count += 1
                    except (ValueError, AttributeError):
                        pass

        return (numeric_count / total_cells) > 0.3 if total_cells > 0 else False

    def download_from_url(self, url: str, output_path: str) -> bool:
        """Download PDF from URL with enhanced URL handling"""
        try:
            logger.info(f"Downloading from URL: {url}")

            # Handle ArXiv URLs - convert abstract URL to PDF URL
            if 'arxiv.org/abs/' in url:
                # Convert https://arxiv.org/abs/2505.17303 to https://arxiv.org/pdf/2505.17303.pdf
                paper_id = url.split('/abs/')[-1]
                url = f"https://arxiv.org/pdf/{paper_id}.pdf"
                logger.info(f"Converted ArXiv URL to PDF: {url}")

            # Handle other common academic URLs
            elif 'researchgate.net' in url and '/publication/' in url:
                logger.warning("ResearchGate URLs may require authentication. Trying direct download...")
            elif 'ieee.org' in url:
                logger.warning("IEEE URLs may require subscription. Trying direct download...")
            elif 'springer.com' in url:
                logger.warning("Springer URLs may require subscription. Trying direct download...")

            # Set headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/pdf,application/octet-stream,*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            response = requests.get(url, stream=True, headers=headers, timeout=30)
            response.raise_for_status()

            # Check if the response is actually a PDF
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and 'octet-stream' not in content_type:
                # Try to detect if it's HTML (redirect page)
                first_chunk = next(response.iter_content(chunk_size=1024), b'')
                if b'<html' in first_chunk.lower() or b'<!doctype' in first_chunk.lower():
                    logger.error("URL returned HTML instead of PDF. This might be a redirect or access-restricted page.")
                    return False

                # Reset the response for actual download
                response = requests.get(url, stream=True, headers=headers, timeout=30)
                response.raise_for_status()

            # Download the file
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # Show progress for large files
                        if total_size > 0 and downloaded_size % (1024 * 1024) == 0:  # Every MB
                            progress = (downloaded_size / total_size) * 100
                            logger.info(f"Download progress: {progress:.1f}%")

            # Verify the downloaded file is a valid PDF
            try:
                with fitz.open(output_path) as test_doc:
                    if test_doc.page_count == 0:
                        logger.error("Downloaded file appears to be empty or corrupted")
                        return False
            except Exception as e:
                logger.error(f"Downloaded file is not a valid PDF: {e}")
                return False

            logger.info(f"Successfully downloaded PDF to: {output_path} ({downloaded_size:,} bytes)")
            return True

        except requests.exceptions.Timeout:
            logger.error("Download timeout - the server took too long to respond")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("Connection error - unable to reach the server")
            return False
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error {e.response.status_code}: {e}")
            if e.response.status_code == 403:
                logger.error("Access forbidden - the PDF might require authentication or subscription")
            elif e.response.status_code == 404:
                logger.error("PDF not found - check if the URL is correct")
            return False
        except Exception as e:
            logger.error(f"Error downloading from URL: {str(e)}")
            return False

class TextSegmenter:
    """Segments scientific papers into logical sections"""

    def __init__(self):
        self.section_patterns = {
            'title': [r'^title[:\s]', r'^[A-Z][^.!?]*$'],
            'abstract': [r'^abstract[:\s]', r'^summary[:\s]'],
            'introduction': [r'^introduction[:\s]', r'^1\.\s*introduction'],
            'methods': [r'^methods[:\s]', r'^methodology[:\s]', r'^2\.\s*methods'],
            'results': [r'^results[:\s]', r'^3\.\s*results', r'^findings[:\s]'],
            'discussion': [r'^discussion[:\s]', r'^4\.\s*discussion'],
            'conclusion': [r'^conclusion[:\s]', r'^conclusions[:\s]'],
            'references': [r'^references[:\s]', r'^bibliography[:\s]'],
            'acknowledgments': [r'^acknowledgments[:\s]', r'^acknowledgements[:\s]']
        }

    def segment_text(self, text: str) -> Dict[str, str]:
        """Segment text into scientific paper sections"""
        sections = {}
        lines = text.split('\n')
        current_section = 'unknown'
        section_content = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if line matches any section pattern
            matched_section = self._match_section(line)
            if matched_section:
                # Save previous section
                if section_content:
                    sections[current_section] = '\n'.join(section_content)

                # Start new section
                current_section = matched_section
                section_content = [line]
            else:
                section_content.append(line)

        # Save last section
        if section_content:
            sections[current_section] = '\n'.join(section_content)

        return sections

    def _match_section(self, line: str) -> Optional[str]:
        """Check if line matches any section pattern"""
        for section, patterns in self.section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    return section
        return None

class KeyPhraseExtractor:
    """Extract key phrases and concepts from text"""

    def __init__(self):
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Using basic extraction.")
            self.nlp = None

        self.stop_words = set(stopwords.words('english'))
        self.lemmatizer = WordNetLemmatizer()

        # Scientific terms patterns
        self.science_patterns = {
            'models': [r'\b\w*model\w*\b', r'\b\w*algorithm\w*\b', r'\b\w*method\w*\b'],
            'metrics': [r'\b\w*accuracy\w*\b', r'\b\w*precision\w*\b', r'\b\w*recall\w*\b',
                       r'\b\w*score\w*\b', r'\b\w*error\w*\b'],
            'datasets': [r'\b\w*dataset\w*\b', r'\b\w*data\s+set\w*\b', r'\b\w*corpus\w*\b'],
            'techniques': [r'\b\w*learning\w*\b', r'\b\w*training\w*\b', r'\b\w*optimization\w*\b']
        }

    def extract_keyphrases(self, text: str, n_phrases: int = 20) -> Dict:
        """Extract key phrases using multiple methods"""
        result = {
            'tfidf_keywords': self._extract_tfidf_keywords(text, n_phrases),
            'named_entities': self._extract_named_entities(text),
            'scientific_terms': self._extract_scientific_terms(text),
            'noun_phrases': self._extract_noun_phrases(text),
            'statistics': self._calculate_text_stats(text)
        }

        return result

    def _extract_tfidf_keywords(self, text: str, n_keywords: int) -> List[Tuple[str, float]]:
        """Extract keywords using TF-IDF"""
        try:
            # Preprocess text
            sentences = sent_tokenize(text)

            vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 3),
                min_df=2
            )

            tfidf_matrix = vectorizer.fit_transform(sentences)
            feature_names = vectorizer.get_feature_names_out()

            # Get average TF-IDF scores
            mean_scores = tfidf_matrix.mean(axis=0).A1
            keywords = [(feature_names[i], mean_scores[i])
                       for i in range(len(feature_names))]

            return sorted(keywords, key=lambda x: x[1], reverse=True)[:n_keywords]
        except Exception as e:
            logger.error(f"TF-IDF extraction error: {str(e)}")
            return []

    def _extract_named_entities(self, text: str) -> List[Dict]:
        """Extract named entities using spaCy"""
        if not self.nlp:
            return []

        try:
            doc = self.nlp(text[:1000000])  # Limit text size
            entities = []

            for ent in doc.ents:
                entities.append({
                    'text': ent.text,
                    'label': ent.label_,
                    'description': spacy.explain(ent.label_)
                })

            return entities
        except Exception as e:
            logger.error(f"Named entity extraction error: {str(e)}")
            return []

    def _extract_scientific_terms(self, text: str) -> Dict[str, List[str]]:
        """Extract scientific terms using pattern matching"""
        scientific_terms = {}

        for category, patterns in self.science_patterns.items():
            terms = set()
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                terms.update([match.lower().strip() for match in matches])
            scientific_terms[category] = list(terms)[:10]  # Limit results

        return scientific_terms

    def _extract_noun_phrases(self, text: str) -> List[str]:
        """Extract noun phrases"""
        if not self.nlp:
            return []

        try:
            doc = self.nlp(text[:500000])  # Limit text size
            noun_phrases = []

            for chunk in doc.noun_chunks:
                if len(chunk.text.split()) >= 2:  # Multi-word phrases
                    noun_phrases.append(chunk.text.strip())

            return list(set(noun_phrases))[:20]  # Remove duplicates and limit
        except Exception as e:
            logger.error(f"Noun phrase extraction error: {str(e)}")
            return []

    def _calculate_text_stats(self, text: str) -> Dict:
        """Calculate text statistics"""
        try:
            words = word_tokenize(text.lower())
            words = [w for w in words if w.isalpha() and w not in self.stop_words]

            return {
                'total_words': len(word_tokenize(text)),
                'unique_words': len(set(words)),
                'sentences': len(sent_tokenize(text)),
                'avg_words_per_sentence': len(word_tokenize(text)) / max(len(sent_tokenize(text)), 1),
                'readability_score': flesch_reading_ease(text[:10000]),  # Limit for performance
                'grade_level': flesch_kincaid_grade(text[:10000]),
                'most_common_words': Counter(words).most_common(10)
            }
        except Exception as e:
            logger.error(f"Text statistics error: {str(e)}")
            return {}

class SciMLPlatform:
    """Main platform class that orchestrates the entire pipeline"""

    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        self.parser = DocumentParser()
        self.segmenter = TextSegmenter()
        self.extractor = KeyPhraseExtractor()

        logger.info("SciML Platform initialized")

    def process_document(self, input_path: str, is_url: bool = False) -> Dict:
        """Process a single document through the entire pipeline"""
        logger.info(f"Processing {'URL' if is_url else 'file'}: {input_path}")

        # Step 1: Handle input
        if is_url:
            filename = f"downloaded_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            local_path = self.output_dir / filename
            if not self.parser.download_from_url(input_path, str(local_path)):
                return {"error": "Failed to download from URL"}
            processing_path = str(local_path)
        else:
            processing_path = input_path

        # Step 2: Parse document with enhanced features
        parsed_data = self.parser.extract_from_pdf(processing_path)
        if not parsed_data:
            return {"error": "Failed to parse document"}

        # Step 3: Segment text
        logger.info("Segmenting text into sections...")
        sections = self.segmenter.segment_text(parsed_data['text'])
        parsed_data['sections'] = sections

        # Step 4: Extract insights
        logger.info("Extracting key phrases and insights...")
        insights = self.extractor.extract_keyphrases(parsed_data['text'])
        parsed_data['insights'] = insights

        # Step 5: Build structured dataset
        logger.info("Building structured dataset...")
        dataset = self.parser.dataset_builder.build_dataset(parsed_data)

        # Step 6: Compile comprehensive results
        result = {
            'metadata': parsed_data['metadata'],
            'sections': sections,
            'insights': insights,
            'equations': parsed_data['equations'],
            'images': parsed_data['images'],
            'tables': parsed_data['tables'],
            'visual_content': parsed_data.get('visual_content', {}),
            'dataset': dataset,
            'processing_timestamp': datetime.now().isoformat(),
            'statistics': {
                'total_images': len(parsed_data['images']),
                'total_tables': len(parsed_data['tables']),
                'total_equations': len(parsed_data['equations']),
                'text_length': len(parsed_data['text']),
                'sections_found': len(sections)
            }
        }

        # Step 7: Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = self.output_dir / f"analysis_{timestamp}.json"

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)

        # Step 8: Export dataset to CSV
        csv_file = self.output_dir / f"dataset_{timestamp}.csv"
        self.parser.dataset_builder.export_to_csv(dataset, str(csv_file))

        logger.info(f"Analysis saved to: {output_file}")
        logger.info(f"Dataset exported to: {csv_file}")
        return result

    def display_results(self, result: Dict):
        """Display analysis results in a formatted way"""
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return

        print("\n" + "="*80)
        print("📊 SCIML PLATFORM - ANALYSIS RESULTS")
        print("="*80)

        # Metadata
        print("\n📄 DOCUMENT METADATA:")
        metadata = result.get('metadata', {})
        print(f"  Title: {metadata.get('title', 'N/A')}")
        print(f"  Author: {metadata.get('author', 'N/A')}")
        print(f"  Pages: {metadata.get('pages', 'N/A')}")
        print(f"  Created: {metadata.get('created', 'N/A')}")

        # Sections
        sections = result.get('sections', {})
        print(f"\n📑 DOCUMENT SECTIONS ({len(sections)}):")
        for section, content in sections.items():
            word_count = len(content.split()) if content else 0
            print(f"  • {section.upper()}: {word_count} words")

        # Key insights
        insights = result.get('insights', {})

        print("\n🔍 TOP KEYWORDS:")
        keywords = insights.get('tfidf_keywords', [])
        if keywords:
            for keyword, score in keywords[:10]:
                print(f"  • {keyword}: {score:.3f}")
        else:
            print("  No keywords extracted")

        print("\n🧬 SCIENTIFIC TERMS:")
        sci_terms = insights.get('scientific_terms', {})
        for category, terms in sci_terms.items():
            if terms:
                print(f"  {category.upper()}: {', '.join(terms[:5])}")

        print("\n🏷️ NAMED ENTITIES:")
        entities = insights.get('named_entities', [])[:10]
        if entities:
            for entity in entities:
                print(f"  • {entity.get('text', '')} ({entity.get('label', '')})")
        else:
            print("  No named entities found")

        # Statistics
        stats = insights.get('statistics', {})
        print("\n📈 TEXT STATISTICS:")
        total_words = stats.get('total_words', 'N/A')
        unique_words = stats.get('unique_words', 'N/A')
        sentences = stats.get('sentences', 'N/A')
        readability = stats.get('readability_score', None)
        grade_level = stats.get('grade_level', None)

        print(f"  • Total words: {total_words}")
        print(f"  • Unique words: {unique_words}")
        print(f"  • Sentences: {sentences}")

        if readability is not None:
            print(f"  • Readability score: {readability:.1f}")
        else:
            print("  • Readability score: N/A")

        if grade_level is not None:
            print(f"  • Grade level: {grade_level:.1f}")
        else:
            print("  • Grade level: N/A")

        # Enhanced visual content info
        print("\n🖼️ VISUAL CONTENT:")
        images = result.get('images', [])
        tables = result.get('tables', [])
        equations = result.get('equations', [])

        print(f"  • Images found: {len(images)}")
        if images:
            image_types = {}
            for img in images:
                img_type = img.get('type', 'unknown')
                image_types[img_type] = image_types.get(img_type, 0) + 1
            for img_type, count in image_types.items():
                print(f"    - {img_type}: {count}")

        print(f"  • Tables found: {len(tables)}")
        if tables:
            numeric_tables = sum(1 for t in tables if t.get('has_numeric_data', False))
            print(f"    - With numeric data: {numeric_tables}")

        print(f"  • Equations found: {len(equations)}")

        # Dataset info
        dataset = result.get('dataset', {})
        if dataset:
            print("\n📊 DATASET STRUCTURE:")
            doc_info = dataset.get('document_info', {})
            print(f"  • Document processed: {doc_info.get('has_images', False) or doc_info.get('has_tables', False) or doc_info.get('has_equations', False)}")
            print(f"  • Structured data available: Yes")

        # Processing statistics
        processing_stats = result.get('statistics', {})
        if processing_stats:
            print("\n⚡ PROCESSING STATISTICS:")
            print(f"  • Text length: {processing_stats.get('text_length', 0):,} characters")
            print(f"  • Sections identified: {processing_stats.get('sections_found', 0)}")
            print(f"  • Total visual elements: {processing_stats.get('total_images', 0) + processing_stats.get('total_tables', 0)}")

        print("\n" + "="*80)

def install_missing_dependencies():
    """Helper function to install missing dependencies"""
    missing_deps = []
    recommended_deps = []

    # Check for OCR engines
    try:
        import easyocr
        print("✅ EasyOCR is available (recommended for web deployment)")
    except ImportError:
        recommended_deps.append("easyocr")

        try:
            import pytesseract
            print("✅ pytesseract is available (requires Tesseract executable)")
        except ImportError:
            missing_deps.append("pytesseract")

    # Check for image processing
    try:
        import cv2
        print("✅ OpenCV is available")
    except ImportError:
        missing_deps.append("opencv-python")

    # Display recommendations
    if recommended_deps:
        print(f"\n🌟 Recommended for web deployment: {', '.join(recommended_deps)}")
        print("To install recommended dependencies:")
        print(f"pip install {' '.join(recommended_deps)}")
        print("Note: EasyOCR is perfect for web deployment (no external dependencies)\n")

    if missing_deps:
        print(f"\n⚠️  Missing optional dependencies: {', '.join(missing_deps)}")
        print("To install them, run:")
        print(f"pip install {' '.join(missing_deps)}")
        print("Note: These are optional for enhanced visual processing features.\n")

    if not missing_deps and not recommended_deps:
        print("\n✅ All optional dependencies are available!")
        print("🚀 Your SciML Platform is fully equipped for advanced processing!")

def main():
    """Main CLI function"""
    # Print OCR status
    if OCR_AVAILABLE:
        if OCR_ENGINE == 'easyocr':
            print("✅ OCR (EasyOCR) is available - perfect for web deployment!")
        elif OCR_ENGINE == 'pytesseract':
            print("✅ OCR (Tesseract) is available")
    else:
        print("⚠️  No OCR engine available. To enable OCR features:")
        print("   For web deployment (recommended): pip install easyocr")
        print("   For desktop use: pip install pytesseract + Tesseract executable")

    parser = argparse.ArgumentParser(
        description="SciML Platform - Scientific Literature Analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python part_1.py document.pdf                    # Analyze local PDF
  python part_1.py --url https://arxiv.org/abs/... # Analyze from ArXiv URL
  python part_1.py document.pdf -o results         # Custom output directory
  python part_1.py document.pdf --quiet            # Quiet mode
        """
    )
    parser.add_argument('input', nargs='?', help='Input PDF file path or URL')
    parser.add_argument('--url', action='store_true', help='Treat input as URL')
    parser.add_argument('--output', '-o', default='output', help='Output directory (default: output)')
    parser.add_argument('--quiet', '-q', action='store_true', help='Quiet mode - reduce logging output')
    parser.add_argument('--install-deps', action='store_true', help='Show missing dependencies info')

    args = parser.parse_args()

    if args.install_deps:
        install_missing_dependencies()
        return

    if not args.input:
        parser.error("Input file path or URL is required (unless using --install-deps)")
        return

    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)

    # Check if input is a URL pattern
    if not args.url and (args.input.startswith('http://') or args.input.startswith('https://')):
        print("🔍 Detected URL input, automatically enabling --url mode")
        args.url = True

    # Initialize platform
    try:
        platform = SciMLPlatform(args.output)
    except Exception as e:
        print(f"❌ Failed to initialize platform: {e}")
        return

    print("🚀 Starting SciML Platform Analysis...")
    print(f"Input: {args.input}")
    print(f"Type: {'URL' if args.url else 'Local file'}")
    print("-" * 50)

    # Process document
    try:
        result = platform.process_document(args.input, args.url)
        platform.display_results(result)

        if 'error' not in result:
            print(f"\n✅ Analysis completed successfully!")
            print(f"📁 Results saved in: {platform.output_dir}")

            # Show file locations
            timestamp = result.get('processing_timestamp', '').replace(':', '').replace('-', '').replace('T', '_')[:15]
            if timestamp:
                print(f"📄 JSON Analysis: {platform.output_dir}/analysis_{timestamp}.json")
                print(f"📊 CSV Dataset: {platform.output_dir}/dataset_{timestamp}.csv")

    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"❌ Error: {str(e)}")
        print("\n💡 Troubleshooting tips:")
        print("  • For URLs: Make sure the URL points directly to a PDF or is from ArXiv")
        print("  • For local files: Check that the file exists and is a valid PDF")
        print("  • Run with --install-deps to check for missing optional dependencies")

if __name__ == "__main__":
    main()