{"metadata": {"title": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "subject": "", "creator": "Microsoft® Word LTSC", "producer": "Microsoft® Word LTSC", "created": "D:20250521183600+03'00'", "modified": "D:20250521183600+03'00'", "pages": 2}, "sections": {"title": "Dashboard to:\no Upload papers.\no View extracted insights.\no Interact with the agent.\no Access APIs and datasets.\n•\nTools: React + Tailwind / Next.js.\n9. Backend & Deployment\n•\nBackend: Node.js / Flask / FastAPI.\n•\nStorage: MongoDB or PostgreSQL.\n•\nAuthentication: JWT or OAuth."}, "insights": {"tfidf_keywords": [["tools", 0.10079188640720667], ["extraction", 0.056357950080456846], ["data", 0.05555555555555555], ["based", 0.049360567579733464], ["platform", 0.047419632810737425], ["model", 0.04448705478295731], ["models", 0.04409646148508646], ["key", 0.040923918065780863], ["upload", 0.039283710065919304], ["js", 0.03770025733287012], ["agent", 0.037378276406457196], ["ml", 0.0372554075777688], ["images", 0.03649946651038004], ["text", 0.03649946651038004], ["apis", 0.030260706918919966], ["datasets", 0.030260706918919966], ["layer", 0.030260706918919966], ["extracted", 0.029242353661639072], ["insights", 0.029242353661639072], ["metadata", 0.028460107352488433]], "named_entities": [{"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "PDF", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "metadata", "label": "PERSON", "description": "People, including fictional"}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "PyMuPDF", "label": "GPE", "description": "Countries, cities, states"}, {"text": "G<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "NLP & Insight Extraction \n• \nText Segmentation: Title", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "KeyBERT", "label": "PERSON", "description": "People, including fictional"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "TableNet", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "ML", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "• \nHandle", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "ML", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nTrain", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "PyTorch", "label": "PERSON", "description": "People, including fictional"}, {"text": "AutoML", "label": "GPE", "description": "Countries, cities, states"}, {"text": "6", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Host", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nModel Context Protocol", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "MCP", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Document model metadata", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "7", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Intelligent Research", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nLLM", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "LLM", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Dashboard", "label": "PERSON", "description": "People, including fictional"}, {"text": "Upload", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "View", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "Access", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "• \nTools:", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "9", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Backend & Deployment", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Node.js / Flask", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nStorage", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "JWT", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "OAuth", "label": "ORG", "description": "Companies, agencies, institutions, etc."}], "scientific_terms": {"models": ["models", "modeling", "statsmodels", "model", "methods"], "metrics": [], "datasets": ["dataset", "datasets"], "techniques": []}, "noun_phrases": ["URL input", "• \nVariable/Concept Identification", "o Document model metadata", "o Access APIs", "Host models", "o Tools", "• \nReverse Engineer Graphs", "Identify models", "Backend & Deployment \n• \nBackend", "usable format", "Dataset Builder \n• \nStructure data", "• \nTools", "Tailwind / Next.js", "• \nTrain or re-run models", "• \nStorage", "• \nLibraries", "SciML Platform", "chart data", "Full System Pipeline", "o Tools: WebPlotDigitizer, custom OpenCV scripts"], "statistics": {"total_words": 360, "unique_words": 140, "sentences": 36, "avg_words_per_sentence": 10.0, "readability_score": 35.65253218884121, "grade_level": 10.282504069853488, "most_common_words": [["tools", 5], ["platform", 3], ["extraction", 3], ["models", 3], ["model", 3], ["pipeline", 2], ["layer", 2], ["upload", 2], ["text", 2], ["metadata", 2]]}}, "equations": [], "images": [], "tables": [], "visual_content": {}, "dataset": {"document_info": {"title": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "page_count": 0, "creation_date": "", "word_count": 262, "has_images": false, "has_tables": false, "has_equations": false}, "content_structure": {"text_content": {"full_text": "  SciML Platform – Full System Pipeline \nHere’s a clear end-to-end pipeline of what the platform will do: \n \n1. Ingestion Layer \n• \nUser Uploads: PDF upload or URL input. \n• \nParser: Extracts text, metadata, equations, and images. \no Tools: pdfplumber, PyMuPDF, or Grobid. \n \n2. NLP & Insight Extraction \n• \nText Segmentation: Title, abstract, introduction, methods, results, etc. \n• \nKey Phrase Extraction: Using spaCy, KeyBERT, or Transformers. \n• \nVariable/Concept Identification: Identify models, metrics, and key scientific terms. \n \n3. Visual Content Extraction \n• \nTable & Figure Detector: \no Extract tables and detect figures/images. \no Tools: pdf2image, OCR (Tesseract), TableNet, or ML-based detectors. \n• \nReverse Engineer Graphs: \no Use image processing to reconstruct chart data. \no Tools: WebPlotDigitizer, custom OpenCV scripts. \n \n4. Dataset Builder \n• \nStructure data into usable format (CSV/JSON). \n• \nHandle missing values, normalize units. \n \n5. Modeling Engine \n\n• \nAuto-select appropriate ML/statistical model. \n• \nTrain or re-run models based on paper's description. \n• \nLibraries: scikit-learn, statsmodels, PyTorch, or AutoML. \n \n6. Output Layer \n• \nAPI Generator: \no Host models and datasets via REST APIs (FastAPI/Flask). \n• \nModel Context Protocol (MCP): \no Document model metadata (inputs, outputs, assumptions, context). \n \n7. Intelligent Research Agent \n• \nLLM-based chatbot that answers questions based on extracted paper insights. \n• \nTools: OpenAI API or locally hosted LLM (if needed). \n \n8. Web Platform (Frontend) \n• \nDashboard to: \no Upload papers. \no View extracted insights. \no Interact with the agent. \no Access APIs and datasets. \n• \nTools: React + Tailwind / Next.js. \n \n9. Backend & Deployment \n• \nBackend: Node.js / Flask / FastAPI. \n• \nStorage: MongoDB or PostgreSQL. \n• \nAuthentication: JWT or OAuth. \n", "sections": {"title": "Dashboard to:\no Upload papers.\no View extracted insights.\no Interact with the agent.\no Access APIs and datasets.\n•\nTools: React + Tailwind / Next.js.\n9. Backend & Deployment\n•\nBackend: Node.js / Flask / FastAPI.\n•\nStorage: MongoDB or PostgreSQL.\n•\nAuthentication: JWT or OAuth."}, "equations": []}, "visual_content": {"images": [], "tables": []}}, "extracted_data": {"keywords": [["tools", 0.10079188640720667], ["extraction", 0.056357950080456846], ["data", 0.05555555555555555], ["based", 0.049360567579733464], ["platform", 0.047419632810737425], ["model", 0.04448705478295731], ["models", 0.04409646148508646], ["key", 0.040923918065780863], ["upload", 0.039283710065919304], ["js", 0.03770025733287012], ["agent", 0.037378276406457196], ["ml", 0.0372554075777688], ["images", 0.03649946651038004], ["text", 0.03649946651038004], ["apis", 0.030260706918919966], ["datasets", 0.030260706918919966], ["layer", 0.030260706918919966], ["extracted", 0.029242353661639072], ["insights", 0.029242353661639072], ["metadata", 0.028460107352488433]], "entities": [{"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "PDF", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "metadata", "label": "PERSON", "description": "People, including fictional"}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "PyMuPDF", "label": "GPE", "description": "Countries, cities, states"}, {"text": "G<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "NLP & Insight Extraction \n• \nText Segmentation: Title", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "KeyBERT", "label": "PERSON", "description": "People, including fictional"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "TableNet", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "ML", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Tools", "label": "PERSON", "description": "People, including fictional"}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "• \nHandle", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "ML", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nTrain", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "PyTorch", "label": "PERSON", "description": "People, including fictional"}, {"text": "AutoML", "label": "GPE", "description": "Countries, cities, states"}, {"text": "6", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Host", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nModel Context Protocol", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "MCP", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Document model metadata", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "7", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Intelligent Research", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nLLM", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "LLM", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Dashboard", "label": "PERSON", "description": "People, including fictional"}, {"text": "Upload", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "View", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "Access", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "• \nTools:", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "9", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Backend & Deployment", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Node.js / Flask", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• \nStorage", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "JWT", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "OAuth", "label": "ORG", "description": "Companies, agencies, institutions, etc."}], "scientific_terms": {"models": ["models", "modeling", "statsmodels", "model", "methods"], "metrics": [], "datasets": ["dataset", "datasets"], "techniques": []}, "statistics": {"total_words": 360, "unique_words": 140, "sentences": 36, "avg_words_per_sentence": 10.0, "readability_score": 35.65253218884121, "grade_level": 10.282504069853488, "most_common_words": [["tools", 5], ["platform", 3], ["extraction", 3], ["models", 3], ["model", 3], ["pipeline", 2], ["layer", 2], ["upload", 2], ["text", 2], ["metadata", 2]]}}, "metadata": {"author": "<PERSON><PERSON><PERSON><PERSON>", "creator": "Microsoft® Word LTSC", "producer": "Microsoft® Word LTSC", "created": "D:20250521183600+03'00'", "modified": "D:20250521183600+03'00'", "pages": "2"}}, "processing_timestamp": "2025-06-11T16:04:57.129253", "statistics": {"total_images": 0, "total_tables": 0, "total_equations": 0, "text_length": 1849, "sections_found": 1}}