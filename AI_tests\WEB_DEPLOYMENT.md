# 🌐 SciML Platform - Web Deployment Guide

This guide covers deploying the SciML Platform as a web application with web-suitable OCR capabilities.

## 🚀 Web-Ready Features

### ✅ **EasyOCR Integration**
- **No External Dependencies**: Pure Python implementation
- **Docker Compatible**: Works seamlessly in containers
- **GPU/CPU Support**: Automatic fallback to CPU mode
- **Multi-language Support**: 80+ languages supported
- **High Accuracy**: State-of-the-art text detection

### ✅ **Web-Suitable Architecture**
- **Stateless Processing**: Each request is independent
- **Memory Efficient**: Optimized for server environments
- **Error Handling**: Robust error recovery
- **JSON/CSV Output**: API-friendly formats

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY . .

# Create output directory
RUN mkdir -p output

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "web_app.py"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  sciml-platform:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./output:/app/output
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
```

## 🌐 Flask Web Application

### Basic Web Interface
```python
from flask import Flask, request, jsonify, render_template
from werkzeug.utils import secure_filename
import os
from part_1 import SciMLPlatform

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max

# Initialize SciML Platform
platform = SciMLPlatform()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/analyze', methods=['POST'])
def analyze_document():
    try:
        if 'file' in request.files:
            file = request.files['file']
            if file.filename.endswith('.pdf'):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)
                
                # Process document
                result = platform.process_document(filepath)
                
                # Clean up uploaded file
                os.remove(filepath)
                
                return jsonify(result)
        
        elif 'url' in request.json:
            url = request.json['url']
            result = platform.process_document(url, is_url=True)
            return jsonify(result)
        
        return jsonify({'error': 'No file or URL provided'}), 400
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    app.run(host='0.0.0.0', port=8000, debug=False)
```

## ☁️ Cloud Deployment

### AWS Lambda (Serverless)
```python
import json
import boto3
from part_1 import SciMLPlatform

def lambda_handler(event, context):
    try:
        platform = SciMLPlatform()
        
        # Get URL from event
        url = event.get('url')
        if not url:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'URL required'})
            }
        
        # Process document
        result = platform.process_document(url, is_url=True)
        
        return {
            'statusCode': 200,
            'body': json.dumps(result, default=str)
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }
```

### Google Cloud Run
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: sciml-platform
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
    spec:
      containers:
      - image: gcr.io/PROJECT_ID/sciml-platform
        ports:
        - containerPort: 8000
        env:
        - name: PORT
          value: "8000"
        resources:
          limits:
            memory: "2Gi"
            cpu: "2"
```

## 🔧 Performance Optimization

### Memory Management
```python
# Optimize EasyOCR for web deployment
import gc
import torch

class OptimizedSciMLPlatform(SciMLPlatform):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Force CPU mode for consistent performance
        if hasattr(torch, 'set_num_threads'):
            torch.set_num_threads(2)
    
    def process_document(self, *args, **kwargs):
        try:
            result = super().process_document(*args, **kwargs)
            
            # Clean up memory after processing
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            return result
        except Exception as e:
            # Clean up on error too
            gc.collect()
            raise e
```

### Caching Strategy
```python
from functools import lru_cache
import hashlib

class CachedSciMLPlatform(SciMLPlatform):
    @lru_cache(maxsize=100)
    def _cached_process_url(self, url_hash):
        # Cache results for frequently accessed URLs
        return super().process_document(url, is_url=True)
    
    def process_document(self, input_path, is_url=False):
        if is_url:
            # Create hash for caching
            url_hash = hashlib.md5(input_path.encode()).hexdigest()
            return self._cached_process_url(url_hash)
        else:
            return super().process_document(input_path, is_url)
```

## 📊 Monitoring & Logging

### Health Check Endpoint
```python
@app.route('/health')
def health_check():
    try:
        # Test OCR availability
        import easyocr
        reader = easyocr.Reader(['en'], gpu=False)
        
        return jsonify({
            'status': 'healthy',
            'ocr_engine': 'easyocr',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500
```

### Metrics Collection
```python
import time
from prometheus_client import Counter, Histogram, generate_latest

# Metrics
REQUEST_COUNT = Counter('requests_total', 'Total requests')
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')
ERROR_COUNT = Counter('errors_total', 'Total errors')

@app.route('/metrics')
def metrics():
    return generate_latest()
```

## 🔒 Security Considerations

### Input Validation
```python
ALLOWED_EXTENSIONS = {'pdf'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_url(url):
    # Validate URL format and allowed domains
    allowed_domains = ['arxiv.org', 'example.com']
    # Add validation logic
    return True
```

### Rate Limiting
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@app.route('/api/analyze', methods=['POST'])
@limiter.limit("10 per minute")
def analyze_document():
    # Processing logic
    pass
```

## 🚀 Deployment Checklist

- [ ] Install EasyOCR and dependencies
- [ ] Configure environment variables
- [ ] Set up file upload limits
- [ ] Implement error handling
- [ ] Add health checks
- [ ] Configure logging
- [ ] Set up monitoring
- [ ] Implement rate limiting
- [ ] Test with sample documents
- [ ] Deploy to staging environment
- [ ] Performance testing
- [ ] Security audit
- [ ] Production deployment

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancers
- Implement session-less design
- Cache frequently accessed results
- Use message queues for long-running tasks

### Vertical Scaling
- Optimize memory usage
- Use GPU acceleration when available
- Implement connection pooling
- Monitor resource usage

The SciML Platform is now fully optimized for web deployment with EasyOCR providing reliable, containerized OCR capabilities!
