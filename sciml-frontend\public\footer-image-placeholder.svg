<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#003f5c" />
      <stop offset="50%" stop-color="#2f4b7c" />
      <stop offset="100%" stop-color="#00b5ad" />
    </linearGradient>
    <linearGradient id="text-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#00b5ad" />
      <stop offset="100%" stop-color="#f0f4f8" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bg-gradient)" />
  
  <!-- Grid pattern -->
  <path d="M0 50 L400 50 M0 100 L400 100 M0 150 L400 150 M0 200 L400 200 M0 250 L400 250 M50 0 L50 300 M100 0 L100 300 M150 0 L150 300 M200 0 L200 300 M250 0 L250 300 M300 0 L300 300 M350 0 L350 300" stroke="#f0f4f8" stroke-width="0.5" opacity="0.1" />
  
  <!-- Decorative elements -->
  <circle cx="200" cy="150" r="80" fill="none" stroke="url(#text-gradient)" stroke-width="2" opacity="0.7" />
  <circle cx="200" cy="150" r="40" fill="none" stroke="url(#text-gradient)" stroke-width="2" opacity="0.7" />
  
  <!-- Image icon -->
  <rect x="150" y="100" width="100" height="80" rx="5" fill="none" stroke="#f0f4f8" stroke-width="2" />
  <line x1="150" y1="180" x2="200" y2="130" stroke="#f0f4f8" stroke-width="2" />
  <line x1="250" y1="180" x2="200" y2="130" stroke="#f0f4f8" stroke-width="2" />
  <circle cx="175" cy="125" r="10" fill="#f0f4f8" opacity="0.7" />
  
  <!-- Text placeholder -->
  <text x="200" y="220" font-family="Arial, sans-serif" font-size="16" fill="#f0f4f8" text-anchor="middle">Image Placeholder</text>
  <text x="200" y="240" font-family="Arial, sans-serif" font-size="12" fill="#f0f4f8" text-anchor="middle">Replace with your own image</text>
</svg>
