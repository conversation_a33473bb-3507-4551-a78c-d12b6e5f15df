import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Navbar from './components/Navbar';
import Home from './components/Home';
import Contact from './components/Contact';
import Insights from './components/Insights';
import Dashboard from './components/Dashboard';
import UploadPaper from './components/UploadPaper';
import APIs from './components/APIs';
import Profile from './components/Profile';
import UsageStats from './components/dashboard/analytics/UsageStats';
import ModelPerformance from './components/dashboard/analytics/ModelPerformance';
import APIUsage from './components/dashboard/analytics/APIUsage';
import DataInsights from './components/dashboard/analytics/DataInsights';
import DataManagement from './components/dashboard/DataManagement';
import Settings from './components/dashboard/Settings';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <Toaster position="top-right" />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/insights" element={<Insights />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/upload-paper" element={<UploadPaper />} />
          <Route path="/dashboard/apis" element={<APIs />} />
          <Route path="/dashboard/profile" element={<Profile />} />
          
          {/* Analytics Routes */}
          <Route path="/dashboard/analytics/usage" element={<UsageStats />} />
          <Route path="/dashboard/analytics/performance" element={<ModelPerformance />} />
          <Route path="/dashboard/analytics/reports" element={<APIUsage />} />
          
          {/* Data Management Route */}
          <Route path="/dashboard/data" element={<DataManagement />} />
          
          {/* Settings Route */}
          <Route path="/dashboard/settings" element={<Settings />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App; 