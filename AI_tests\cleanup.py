#!/usr/bin/env python3
"""
SciML Platform - Storage Cleanup Utility
Cleans up old processing status and results files
"""

import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

# Storage directories
STORAGE_DIR = Path('storage')
STATUS_DIR = STORAGE_DIR / 'status'
RESULTS_DIR = STORAGE_DIR / 'results'

def cleanup_old_files(max_age_hours=24):
    """Clean up files older than max_age_hours"""
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    cutoff_timestamp = cutoff_time.timestamp()
    
    cleaned_count = 0
    
    # Clean status files
    if STATUS_DIR.exists():
        for status_file in STATUS_DIR.glob('*.json'):
            try:
                file_mtime = status_file.stat().st_mtime
                if file_mtime < cutoff_timestamp:
                    # Check if task is completed or errored
                    with open(status_file, 'r') as f:
                        status_data = json.load(f)
                    
                    if status_data.get('status') in ['completed', 'error']:
                        status_file.unlink()
                        cleaned_count += 1
                        print(f"Cleaned status file: {status_file.name}")
            except Exception as e:
                print(f"Error cleaning {status_file}: {e}")
    
    # Clean results files
    if RESULTS_DIR.exists():
        for results_file in RESULTS_DIR.glob('*.json'):
            try:
                file_mtime = results_file.stat().st_mtime
                if file_mtime < cutoff_timestamp:
                    # Check if corresponding status file exists
                    task_id = results_file.stem
                    status_file = STATUS_DIR / f"{task_id}.json"
                    
                    if not status_file.exists():
                        results_file.unlink()
                        cleaned_count += 1
                        print(f"Cleaned orphaned results file: {results_file.name}")
            except Exception as e:
                print(f"Error cleaning {results_file}: {e}")
    
    print(f"Cleanup completed. Removed {cleaned_count} files.")
    return cleaned_count

def get_storage_stats():
    """Get storage statistics"""
    stats = {
        'status_files': 0,
        'results_files': 0,
        'total_size_mb': 0,
        'active_tasks': 0,
        'completed_tasks': 0,
        'error_tasks': 0
    }
    
    # Count status files
    if STATUS_DIR.exists():
        for status_file in STATUS_DIR.glob('*.json'):
            try:
                stats['status_files'] += 1
                stats['total_size_mb'] += status_file.stat().st_size / (1024 * 1024)
                
                with open(status_file, 'r') as f:
                    status_data = json.load(f)
                
                status = status_data.get('status', 'unknown')
                if status == 'processing':
                    stats['active_tasks'] += 1
                elif status == 'completed':
                    stats['completed_tasks'] += 1
                elif status == 'error':
                    stats['error_tasks'] += 1
                    
            except Exception as e:
                print(f"Error reading {status_file}: {e}")
    
    # Count results files
    if RESULTS_DIR.exists():
        for results_file in RESULTS_DIR.glob('*.json'):
            try:
                stats['results_files'] += 1
                stats['total_size_mb'] += results_file.stat().st_size / (1024 * 1024)
            except Exception as e:
                print(f"Error reading {results_file}: {e}")
    
    return stats

def main():
    """Main cleanup function"""
    print("🧹 SciML Platform Storage Cleanup")
    print("=" * 40)
    
    # Show current stats
    stats = get_storage_stats()
    print(f"📊 Current Storage Stats:")
    print(f"  Status files: {stats['status_files']}")
    print(f"  Results files: {stats['results_files']}")
    print(f"  Total size: {stats['total_size_mb']:.2f} MB")
    print(f"  Active tasks: {stats['active_tasks']}")
    print(f"  Completed tasks: {stats['completed_tasks']}")
    print(f"  Error tasks: {stats['error_tasks']}")
    print()
    
    # Cleanup old files (older than 24 hours)
    print("🧹 Cleaning up old files...")
    cleaned_count = cleanup_old_files(max_age_hours=24)
    
    if cleaned_count > 0:
        # Show updated stats
        print()
        stats = get_storage_stats()
        print(f"📊 Updated Storage Stats:")
        print(f"  Status files: {stats['status_files']}")
        print(f"  Results files: {stats['results_files']}")
        print(f"  Total size: {stats['total_size_mb']:.2f} MB")
    
    print("\n✅ Cleanup completed!")

if __name__ == "__main__":
    main()
