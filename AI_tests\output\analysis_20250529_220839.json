{"metadata": {"format": "PDF 1.5", "title": "", "author": "", "subject": "", "keywords": "", "creator": "TeX", "producer": "pdfTeX-1.40.26", "creationDate": "D:20250522200704Z", "modDate": "D:20250522200704Z", "trapped": "", "encryption": null, "page_count": 8}, "sections": {"title": "recognition based on deep learning for quadcopters flight control,” putingforinfrastructure-assistedautonomousuavs,”inICC2020-2020\nInternationalJournalofComputing,vol.17,pp.17–29,2024. IEEEInternationalConferenceonCommunications(ICC). IEEE,2020,\n[12] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, pp.1–6.\n“Gesture recognition based on deep learning for quadcopters flight [22] ——,“Ameasurementstudyonedgecomputingforautonomousuavs,”\ncontrol,”preprintarXiv:2306.04319,2023. inProceedingsoftheACMSIGCOMM2019WorkshoponMobileAir-\n[13] <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON>, “Uav-gesture: A dataset for GroundEdgeComputing,Systems,Networks,andApplications. IEEE,\nuav control and gesture recognition,” Proceedings of the European 2019,pp.29–35.\nConferenceonComputerVision(ECCV)Workshops,pp.227–243,2018."}, "insights": {"tfidf_keywords": [], "named_entities": [{"text": "UAV Control", "label": "PERSON", "description": "People, including fictional"}, {"text": "Vision", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Hand Gesture\nRecognition", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Edge-Computing", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>\n", "label": "PERSON", "description": "People, including fictional"}, {"text": "Department of Computer Science and Engineering", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "University of Louisville", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "KY", "label": "GPE", "description": "Countries, cities, states"}, {"text": "USA", "label": "GPE", "description": "Countries, cities, states"}, {"text": "5]–[7", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV Control", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Vision", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV Control", "label": "PERSON", "description": "People, including fictional"}, {"text": "AirSim", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "three", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Recognition", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Edge Computing", "label": "PERSON", "description": "People, including fictional"}, {"text": "UnmannedAerialVehicles(UAVs)haverevolutionizedvar-", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "One", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "9", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "The Summary", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "• Custom-trainedhandgesturelandmarkmodel", "label": "PERSON", "description": "People, including fictional"}, {"text": "1,500", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAVs", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "•", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "10", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "• Latency", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "System Model", "label": "PERSON", "description": "People, including fictional"}, {"text": "UAV Control\nII", "label": "PERSON", "description": "People, including fictional"}, {"text": "recent years", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "UAVcontrolsystems", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "1D", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "12", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "13", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV-GESTURE", "label": "PERSON", "description": "People, including fictional"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "14", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "MediaPipe", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "15", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "16", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "CNN", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "1 second", "label": "TIME", "description": "Times smaller than a day"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "CNN", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "max", "label": "PERSON", "description": "People, including fictional"}, {"text": "96.14%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "B. Challenges in Gesture Recognition", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV Control", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "F1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "CNN", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• Accuracy of Gesture Recognition: Achieving high ac- dynamic", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "second", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "first", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "ResNet50", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "ImageNet", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "six", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "74.80%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "97.60%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "99.89%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "third", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "• User Training: Users", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "second", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "third", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "IV", "label": "GPE", "description": "Countries, cities, states"}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "Landmark-", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "0.0001", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "0.01", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "0.001", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "six", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "32", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "1,500", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "0.4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "Neural Network Architecture", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "20", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "four", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "96.14%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "ReLU", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "two", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "first", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "Extended Distance", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "One", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "max", "label": "PERSON", "description": "People, including fictional"}, {"text": "224,224,3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "a few meters", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "5 meters", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "F", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "17", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "18", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "MediaPipe", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "l l l−1", "label": "NORP", "description": "Nationalities or religious or political groups"}, {"text": "1 to 10 meters", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "1–5 meter", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "W", "label": "PERSON", "description": "People, including fictional"}, {"text": "l l", "label": "NORP", "description": "Nationalities or religious or political groups"}, {"text": "ReLU", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "Max", "label": "PERSON", "description": "People, including fictional"}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "l l\nV. EDGE-ASSISTEDDISTRIBUTEDCOMPUTING", "label": "NORP", "description": "Nationalities or religious or political groups"}, {"text": "P", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Raspberry Pi", "label": "PERSON", "description": "People, including fictional"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "19", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "W", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "WiFi", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "2.4GHz", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "• Video Streaming", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "UDP", "label": "GPE", "description": "Countries, cities, states"}, {"text": "11111", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "720p", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "15", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "FPS", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "• State Information", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "10ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "10Hz", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "System", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "Edge", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV Control", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "15%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Failsafe Mechanisms", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "500ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "one", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "First", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "theUAVtriggersautomatichovermode", "label": "PERSON", "description": "People, including fictional"}, {"text": "Edge", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "20]–[22", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "one", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "PERFORMANCEEVALUATION", "label": "PERSON", "description": "People, including fictional"}, {"text": "WiFi", "label": "PERSON", "description": "People, including fictional"}, {"text": "Equippedwithoptimized Theexperimentswereconductedinbothsimulatedandreal-", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "AirSim", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "DJITellodroneforreal", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "224x224pixels", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "1-3meters", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "96.14%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "ClassificationModel", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "6", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "CNN", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "96.14%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "AirSim from1mto5m", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "the ex- 90%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "AirSim", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "two", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "400", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "UAV", "label": "PERSON", "description": "People, including fictional"}, {"text": "2 meters", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Tell<PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "720p", "label": "NORP", "description": "Nationalities or religious or political groups"}, {"text": "servedlessthan5%degradationinperformance", "label": "PERSON", "description": "People, including fictional"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Throughoutalltests", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "TensorFlow", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "three", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "MediaPipe", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Tell<PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "720p", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Video Stream", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Control commands", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "WiFi", "label": "PERSON", "description": "People, including fictional"}, {"text": "720p", "label": "PRODUCT", "description": "Objects, vehicles, foods, etc. (not services)"}, {"text": "80ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "120ms", "label": "GPE", "description": "Countries, cities, states"}, {"text": "RESULTS Edge Processing Latency", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "7", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "three", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "CPU", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "22ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "30", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "FPS", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "28ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "25", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "FPS", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "35ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "20", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "FPS", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Fig", "label": "PERSON", "description": "People, including fictional"}, {"text": "7", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "Edge Computing Capacity\nFig", "label": "FAC", "description": "Buildings, airports, highways, bridges, etc."}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "10 meters", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "12ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "150ms", "label": "FAC", "description": "Buildings, airports, highways, bridges, etc."}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Position Accuracy: We measure the position accuracy", "label": "WORK_OF_ART", "description": "Titles of books, songs, etc."}, {"text": "30ms", "label": "ORDINAL", "description": "\"first\", \"second\", etc."}, {"text": "ACKNOWLEDGMENT", "label": "GPE", "description": "Countries, cities, states"}, {"text": "92%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "We\nFoundation", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "NSF", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "1849213", "label": "MONEY", "description": "Monetary values, including unit"}, {"text": "UAV", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "18cm", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "96%", "label": "PERCENT", "description": "Percentage, including \"%\""}, {"text": "1", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "Proceedings of the Sustainable Research", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "2", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "C.<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "L.S.Khoo", "label": "GPE", "description": "Countries, cities, states"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "8.3m/s", "label": "QUANTITY", "description": "Measurements, as of weight or distance"}, {"text": "Science & Justice", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "62", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "50−1000", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "3", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON> <PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "14", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "4", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "15", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "<PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "C.<PERSON><PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "5", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "AR/VR", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "IEEE", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Physically Interacting", "label": "PERSON", "description": "People, including fictional"}, {"text": "16", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "2021", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "6", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "IEEE Sensors Journal", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "22", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "24540,2022", "label": "GPE", "description": "Countries, cities, states"}, {"text": "17", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "uavinteractionapproachinrealtime,”Sensors", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "18", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "2022", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "8", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "IEEE Access", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "131543–131558,2019", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "19", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "9", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "GPE", "description": "Countries, cities, states"}, {"text": "2023", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "IEEE International Conference", "label": "EVENT", "description": "Named hurricanes, battles, wars, sports events, etc."}, {"text": "SMARTCOMP", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "Robotics", "label": "PERSON", "description": "People, including fictional"}, {"text": "IEEE,2023,pp.219–221", "label": "GPE", "description": "Countries, cities, states"}, {"text": "20", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "vol.17,pp.17–29", "label": "GPE", "description": "Countries, cities, states"}, {"text": "2019", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "IEEE Military Communications Conference", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "2020", "label": "DATE", "description": "Absolute or relative dates or periods"}, {"text": "2019,pp.40–45", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "11", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "21", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "12", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "pp.1–6", "label": "PERSON", "description": "People, including fictional"}, {"text": "22", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "control,”preprintarXiv:2306.04319,2023", "label": "GPE", "description": "Countries, cities, states"}, {"text": "13", "label": "CARDINAL", "description": "Numerals that do not fall under another type"}, {"text": "<PERSON><PERSON> <PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "<PERSON><PERSON>", "label": "PERSON", "description": "People, including fictional"}, {"text": "Systems", "label": "GPE", "description": "Countries, cities, states"}, {"text": "Networks", "label": "ORG", "description": "Companies, agencies, institutions, etc."}, {"text": "the European 2019,pp.29–35", "label": "LOC", "description": "Non-GPE locations, mountain ranges, bodies of water"}, {"text": "pp.227–243,2018", "label": "ORG", "description": "Companies, agencies, institutions, etc."}], "scientific_terms": {"models": ["ourcustomlandmarkmodelistrainedonalocallycollected", "methods", "needtofinetuneagesturerecognitionmodelthatcanberobust", "methodologies", "basedmethodalthoughslightly", "analysisoffourhandgesturedetectionmethodologiestailored", "basedmodelincursaverylowfalsepositiverate", "trainedhandgesturelandmarkmodel", "sidegesturerecognitionmethod", "modelinputimagesize"], "metrics": ["inaccuracy", "wealsomeasuretheaccuracyofthelandmark", "precision", "scores", "detectionaccuracy", "errors", "accuracy", "trainingaccuracyprogressively", "recall", "error"], "datasets": ["dataset", "modeltrainedonalocallycollecteddatasetofsixclasses"], "techniques": ["training", "usingadeeplearningmodeloptimized", "trainingaccuracyprogressively", "theuavsareextremelychallengingasthedeeplearningbased", "learning"]}, "noun_phrases": ["safe operation", "all\nFig.5", "the DJI recognition model", "regardless sive hyperparameter tuning", "the loop", "Information autonomy", "diverse applications", "high-frequency neural network", "edge-assisted landmark-based gesture recognition", "the model", "variable background noise", "500ms delays", "[10] <PERSON><PERSON>,“Captainglove", "convolution operations", "25 FPS", "the loss", "low computational requirements", "planned paths", "varying distances", "a testing accuracy"], "statistics": {}}, "equations": ["F =σ(W ∗X +b ) (1)\nl l l−1 l\n1 to 10 meters, surpassing the original 1–5 meter range", "P =max pool(F ) (2)\nl l\nV", "y =softmax(W ·P +b ) (3) challenges due to hardware limitations [19]"], "images_info": [{"page": 2, "index": 0, "width": 1365, "height": 1842}, {"page": 4, "index": 0, "width": 1315, "height": 747}, {"page": 4, "index": 1, "width": 3476, "height": 1230}, {"page": 5, "index": 0, "width": 1494, "height": 2475}, {"page": 6, "index": 0, "width": 1848, "height": 851}, {"page": 6, "index": 1, "width": 1067, "height": 685}, {"page": 7, "index": 0, "width": 1536, "height": 1024}, {"page": 7, "index": 1, "width": 712, "height": 483}], "tables_count": 1, "processing_timestamp": "2025-05-29T22:08:39.331924"}