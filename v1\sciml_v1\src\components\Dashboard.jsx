import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import DashboardLayout from './DashboardLayout';
import './styles/Dashboard.css';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const { user } = useAuth();

  // Sample data for dashboard
  const dashboardData = {
    papersProcessed: 42,
    modelsCreated: 15,
    apiCalls: 2784,
    storageUsed: 68, // in percentage
    recentPapers: [
      {
        id: 1,
        title: 'Quantum Machine Learning for Scientific Discovery',
        date: '2023-06-15',
        status: 'Processed',
        models: 3
      },
      {
        id: 2,
        title: 'Neural Networks for Protein Folding Prediction',
        date: '2023-06-10',
        status: 'Processing',
        models: 0
      },
      {
        id: 3,
        title: 'Climate Change Prediction Using Deep Learning',
        date: '2023-06-05',
        status: 'Processed',
        models: 2
      },
      {
        id: 4,
        title: 'Reinforcement Learning in Molecular Dynamics',
        date: '2023-05-28',
        status: 'Failed',
        models: 0
      }
    ],
    apiUsage: [
      { date: 'Mon', calls: 320 },
      { date: 'Tue', calls: 480 },
      { date: 'Wed', calls: 590 },
      { date: 'Thu', calls: 390 },
      { date: 'Fri', calls: 420 },
      { date: 'Sat', calls: 280 },
      { date: 'Sun', calls: 304 }
    ]
  };



  // Status badge color mapping
  const statusColors = {
    'Processed': 'bg-green-500',
    'Processing': 'bg-blue-500',
    'Failed': 'bg-red-500',
    'Pending': 'bg-yellow-500'
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <motion.div
          className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-accent">
                Welcome back, {user?.name?.split(' ')[0]}!
              </h1>
              <p className="mt-1 text-primary">
                Here's what's happening with your research today.
              </p>
            </div>
            <div className="hidden md:block">
              <img
                src={user?.avatar}
                alt={user?.name}
                className="w-16 h-16 rounded-full border-4 border-highlight"
              />
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex items-center">
              <div className="p-2 lg:p-3 rounded-full bg-blue-100">
                <svg className="w-5 h-5 lg:w-6 lg:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-3 lg:ml-4">
                <h2 className="text-xl lg:text-2xl font-bold text-accent">{dashboardData.papersProcessed}</h2>
                <p className="text-primary text-xs lg:text-sm">Papers Processed</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center">
              <div className="p-2 lg:p-3 rounded-full bg-green-100">
                <svg className="w-5 h-5 lg:w-6 lg:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <div className="ml-3 lg:ml-4">
                <h2 className="text-xl lg:text-2xl font-bold text-accent">{dashboardData.modelsCreated}</h2>
                <p className="text-primary text-xs lg:text-sm">Models Created</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex items-center">
              <div className="p-2 lg:p-3 rounded-full bg-purple-100">
                <svg className="w-5 h-5 lg:w-6 lg:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="ml-3 lg:ml-4">
                <h2 className="text-xl lg:text-2xl font-bold text-accent">{dashboardData.apiCalls.toLocaleString()}</h2>
                <p className="text-primary text-xs lg:text-sm">API Calls</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex items-center">
              <div className="p-2 lg:p-3 rounded-full bg-orange-100">
                <svg className="w-5 h-5 lg:w-6 lg:h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
              <div className="ml-3 lg:ml-4">
                <h2 className="text-xl lg:text-2xl font-bold text-accent">{dashboardData.storageUsed}%</h2>
                <p className="text-primary text-xs lg:text-sm">Storage Used</p>
              </div>
            </div>
            <div className="mt-3 lg:mt-4 w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-highlight h-2 rounded-full transition-all duration-300"
                style={{ width: `${dashboardData.storageUsed}%` }}
              ></div>
            </div>
          </motion.div>
        </div>


        {/* Recent Activity and Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          {/* Recent Papers */}
          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-base lg:text-lg font-semibold text-accent">Recent Papers</h2>
              <button className="text-highlight hover:text-accent text-xs lg:text-sm font-medium">
                View All
              </button>
            </div>
            <div className="space-y-2 lg:space-y-3">
              {dashboardData.recentPapers.slice(0, 5).map((paper, index) => (
                <div key={index} className="flex items-center justify-between p-2 lg:p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-xs lg:text-sm font-medium text-accent truncate">{paper.title}</h3>
                    <p className="text-xs text-primary">{paper.date}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full text-white ${statusColors[paper.status]} ml-2 flex-shrink-0`}>
                    {paper.status}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* API Usage Chart */}
          <motion.div
            className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-base lg:text-lg font-semibold text-accent">API Usage</h2>
              <div className="flex space-x-1 lg:space-x-2">
                <button className="px-2 lg:px-3 py-1 text-xs rounded-full bg-gray-100 text-accent hover:bg-gray-200 transition-colors duration-200">
                  Week
                </button>
                <button className="px-2 lg:px-3 py-1 text-xs rounded-full bg-highlight text-white">
                  Month
                </button>
                <button className="px-2 lg:px-3 py-1 text-xs rounded-full bg-gray-100 text-accent hover:bg-gray-200 transition-colors duration-200">
                  Year
                </button>
              </div>
            </div>
            <div className="h-32 lg:h-48 flex items-end justify-between space-x-1 lg:space-x-2">
              {[65, 45, 78, 52, 89, 67, 43].map((height, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-highlight rounded-t-sm hover:opacity-80 transition-opacity duration-200 cursor-pointer"
                    style={{ height: `${height}%` }}
                  ></div>
                  <span className="text-xs text-primary mt-1 lg:mt-2">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-sm border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <h2 className="text-base lg:text-lg font-semibold text-accent mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
            <Link to="/dashboard/upload" className="flex flex-col items-center p-3 lg:p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
              <svg className="w-6 h-6 lg:w-8 lg:h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
              </svg>
              <span className="text-xs lg:text-sm font-medium text-accent text-center">Upload Paper</span>
            </Link>
            <Link to="/dashboard/models/create" className="flex flex-col items-center p-3 lg:p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
              <svg className="w-6 h-6 lg:w-8 lg:h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              <span className="text-xs lg:text-sm font-medium text-accent text-center">Create Model</span>
            </Link>
            <Link to="/dashboard/analytics/usage" className="flex flex-col items-center p-3 lg:p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
              <svg className="w-6 h-6 lg:w-8 lg:h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span className="text-xs lg:text-sm font-medium text-accent text-center">View Analytics</span>
            </Link>
            <Link to="/dashboard/apis" className="flex flex-col items-center p-3 lg:p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors duration-200">
              <svg className="w-6 h-6 lg:w-8 lg:h-8 text-orange-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="text-xs lg:text-sm font-medium text-accent text-center">API Keys</span>
            </Link>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
