import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const AllPapers = () => {
  const [papers, setPapers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [selectedPapers, setSelectedPapers] = useState([]);

  // Mock data for papers
  useEffect(() => {
    const mockPapers = [
      {
        id: 1,
        title: 'Deep Learning Approaches for Climate Modeling',
        authors: ['Dr. <PERSON>', 'Prof. <PERSON>'],
        uploadDate: '2024-01-15',
        status: 'completed',
        processingTime: '2.5 hours',
        fileSize: '4.2 MB',
        models: 3,
        apis: 2,
        downloads: 156,
        tags: ['Climate', 'Deep Learning', 'Neural Networks']
      },
      {
        id: 2,
        title: 'Quantum Computing Applications in Drug Discovery',
        authors: ['Dr. <PERSON>', 'Dr. <PERSON>'],
        uploadDate: '2024-01-14',
        status: 'processing',
        processingTime: '1.2 hours',
        fileSize: '6.8 MB',
        models: 0,
        apis: 0,
        downloads: 0,
        tags: ['Quantum Computing', 'Drug Discovery', 'Molecular Dynamics']
      },
      {
        id: 3,
        title: 'Machine Learning for Protein Folding Prediction',
        authors: ['Prof. Alex Johnson', 'Dr. Maria Garcia'],
        uploadDate: '2024-01-13',
        status: 'completed',
        processingTime: '3.1 hours',
        fileSize: '8.9 MB',
        models: 5,
        apis: 3,
        downloads: 289,
        tags: ['Protein Folding', 'Machine Learning', 'Bioinformatics']
      },
      {
        id: 4,
        title: 'Neural Networks for Financial Market Prediction',
        authors: ['Dr. Robert Kim', 'Prof. Lisa Zhang'],
        uploadDate: '2024-01-12',
        status: 'failed',
        processingTime: '0.5 hours',
        fileSize: '2.1 MB',
        models: 0,
        apis: 0,
        downloads: 0,
        tags: ['Finance', 'Neural Networks', 'Time Series']
      },
      {
        id: 5,
        title: 'Computer Vision for Medical Image Analysis',
        authors: ['Dr. David Lee', 'Prof. Anna Smith'],
        uploadDate: '2024-01-11',
        status: 'completed',
        processingTime: '4.2 hours',
        fileSize: '12.3 MB',
        models: 7,
        apis: 4,
        downloads: 423,
        tags: ['Computer Vision', 'Medical Imaging', 'CNN']
      }
    ];

    setTimeout(() => {
      setPapers(mockPapers);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort papers
  const filteredPapers = papers
    .filter(paper => {
      const matchesSearch = paper.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           paper.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
                           paper.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesStatus = filterStatus === 'all' || paper.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.uploadDate) - new Date(a.uploadDate);
        case 'title':
          return a.title.localeCompare(b.title);
        case 'status':
          return a.status.localeCompare(b.status);
        case 'downloads':
          return b.downloads - a.downloads;
        default:
          return 0;
      }
    });

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'processing':
        return (
          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const handleSelectPaper = (paperId) => {
    setSelectedPapers(prev =>
      prev.includes(paperId)
        ? prev.filter(id => id !== paperId)
        : [...prev, paperId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPapers.length === filteredPapers.length) {
      setSelectedPapers([]);
    } else {
      setSelectedPapers(filteredPapers.map(paper => paper.id));
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="pt-16 sm:pt-20 px-4 lg:px-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">All Papers</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Manage and monitor your uploaded research papers
              </p>
            </div>
            <div className="mt-4 lg:mt-0">
              <Link
                to="/dashboard/upload"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Upload New Paper
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            { label: 'Total Papers', value: papers.length, color: 'bg-blue-500' },
            { label: 'Completed', value: papers.filter(p => p.status === 'completed').length, color: 'bg-green-500' },
            { label: 'Processing', value: papers.filter(p => p.status === 'processing').length, color: 'bg-yellow-500' },
            { label: 'Failed', value: papers.filter(p => p.status === 'failed').length, color: 'bg-red-500' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-3 h-3 lg:w-4 lg:h-4 rounded-full ${stat.color} mr-3`}></div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search papers, authors, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="processing">Processing</option>
                <option value="failed">Failed</option>
              </select>

              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Date</option>
                <option value="title">Sort by Title</option>
                <option value="status">Sort by Status</option>
                <option value="downloads">Sort by Downloads</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Papers Table */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading papers...</p>
            </div>
          ) : filteredPapers.length === 0 ? (
            <div className="p-8 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No papers found</h3>
              <p className="mt-2 text-primary">
                {searchQuery || filterStatus !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Upload your first research paper to get started'
                }
              </p>
              {!searchQuery && filterStatus === 'all' && (
                <Link
                  to="/dashboard/upload"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Upload Paper
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="bg-gray-50 px-4 lg:px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded"
                    checked={selectedPapers.length === filteredPapers.length}
                    onChange={handleSelectAll}
                  />
                  <span className="ml-3 text-sm font-medium text-accent">
                    {selectedPapers.length > 0 ? `${selectedPapers.length} selected` : 'Select all'}
                  </span>
                  {selectedPapers.length > 0 && (
                    <div className="ml-4 flex space-x-2">
                      <button className="text-sm text-red-600 hover:text-red-800">Delete</button>
                      <button className="text-sm text-highlight hover:text-accent">Export</button>
                    </div>
                  )}
                </div>
              </div>

              {/* Table Content */}
              <div className="divide-y divide-gray-200">
                <AnimatePresence>
                  {filteredPapers.map((paper, index) => (
                    <motion.div
                      key={paper.id}
                      className="p-4 lg:p-6 hover:bg-gray-50 transition-colors duration-200"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <div className="flex items-start space-x-4">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded mt-1"
                          checked={selectedPapers.includes(paper.id)}
                          onChange={() => handleSelectPaper(paper.id)}
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-accent hover:text-highlight transition-colors duration-200">
                                <Link to={`/dashboard/papers/${paper.id}`}>
                                  {paper.title}
                                </Link>
                              </h3>
                              <p className="text-sm text-primary mt-1">
                                by {paper.authors.join(', ')}
                              </p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {paper.tags.map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>

                            <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(paper.status)}`}>
                                {getStatusIcon(paper.status)}
                                <span className="ml-1 capitalize">{paper.status}</span>
                              </div>
                              <div className="text-xs text-primary">
                                Uploaded: {new Date(paper.uploadDate).toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          <div className="mt-4 grid grid-cols-2 lg:grid-cols-5 gap-4 text-sm">
                            <div>
                              <span className="text-primary">File Size:</span>
                              <span className="ml-1 font-medium text-accent">{paper.fileSize}</span>
                            </div>
                            <div>
                              <span className="text-primary">Processing:</span>
                              <span className="ml-1 font-medium text-accent">{paper.processingTime}</span>
                            </div>
                            <div>
                              <span className="text-primary">Models:</span>
                              <span className="ml-1 font-medium text-accent">{paper.models}</span>
                            </div>
                            <div>
                              <span className="text-primary">APIs:</span>
                              <span className="ml-1 font-medium text-accent">{paper.apis}</span>
                            </div>
                            <div>
                              <span className="text-primary">Downloads:</span>
                              <span className="ml-1 font-medium text-accent">{paper.downloads}</span>
                            </div>
                          </div>

                          <div className="mt-4 flex flex-wrap gap-2">
                            <Link
                              to={`/dashboard/papers/${paper.id}`}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                            >
                              View Details
                            </Link>
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                              Download
                            </button>
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default AllPapers;