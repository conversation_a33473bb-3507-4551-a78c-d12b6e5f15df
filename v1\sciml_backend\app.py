#!/usr/bin/env python3
"""
SciML Platform - Web Application Backend
A beautiful web interface for scientific literature analysis
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import threading
import signal
from pathlib import Path

# Import our SciML platform
from part_1 import SciMLPlatform

app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend
app.config['SECRET_KEY'] = 'sciml-platform-2024'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# Create persistent storage directories
STORAGE_DIR = Path('storage')
STATUS_DIR = STORAGE_DIR / 'status'
RESULTS_DIR = STORAGE_DIR / 'results'

# Create directories if they don't exist
STORAGE_DIR.mkdir(exist_ok=True)
STATUS_DIR.mkdir(exist_ok=True)
RESULTS_DIR.mkdir(exist_ok=True)

# Initialize SciML Platform
platform = SciMLPlatform()

# Timeout wrapper for processing
class TimeoutError(Exception):
    pass

def timeout_handler(func, args, timeout_duration=300):  # 5 minutes timeout
    """Execute function with timeout"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func(*args)
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_duration)

    if thread.is_alive():
        # Thread is still running, timeout occurred
        raise TimeoutError(f"Processing timed out after {timeout_duration} seconds")

    if exception[0]:
        raise exception[0]

    return result[0]

# Persistent storage functions
def save_status(task_id, status_data):
    """Save processing status to disk"""
    status_file = STATUS_DIR / f"{task_id}.json"
    with open(status_file, 'w') as f:
        json.dump(status_data, f)

def load_status(task_id):
    """Load processing status from disk"""
    status_file = STATUS_DIR / f"{task_id}.json"
    if status_file.exists():
        with open(status_file, 'r') as f:
            return json.load(f)
    return None

def save_results(task_id, results_data):
    """Save processing results to disk"""
    results_file = RESULTS_DIR / f"{task_id}.json"
    with open(results_file, 'w') as f:
        json.dump(results_data, f, default=str)

def load_results(task_id):
    """Load processing results from disk"""
    results_file = RESULTS_DIR / f"{task_id}.json"
    if results_file.exists():
        with open(results_file, 'r') as f:
            return json.load(f)
    return None

def update_status(task_id, **kwargs):
    """Update specific fields in status"""
    status = load_status(task_id) or {}
    status.update(kwargs)
    save_status(task_id, status)
    return status

@app.route('/')
def index():
    """Main page"""
    return jsonify({
        'message': 'SciML Platform Backend API',
        'version': '1.0.0',
        'status': 'running'
    })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), f"{task_id}_{filename}")
        file.save(temp_path)

        # Start processing in background
        save_status(task_id, {
            'status': 'processing',
            'message': 'Analyzing document...',
            'progress': 0,
            'filename': filename,
            'started_at': datetime.now().isoformat()
        })

        # Process in background thread
        thread = threading.Thread(target=process_document_async, args=(task_id, temp_path, False))
        thread.daemon = True
        thread.start()

        return jsonify({'task_id': task_id, 'status': 'started'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze-url', methods=['POST'])
def analyze_url():
    """Handle URL analysis"""
    try:
        data = request.get_json()
        if not data or 'url' not in data:
            return jsonify({'error': 'No URL provided'}), 400

        url = data['url'].strip()
        if not url:
            return jsonify({'error': 'Empty URL provided'}), 400

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Start processing in background
        save_status(task_id, {
            'status': 'processing',
            'message': 'Downloading and analyzing document...',
            'progress': 0,
            'url': url,
            'started_at': datetime.now().isoformat()
        })

        # Process in background thread
        thread = threading.Thread(target=process_document_async, args=(task_id, url, True))
        thread.daemon = True
        thread.start()

        return jsonify({'task_id': task_id, 'status': 'started'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """Get processing status"""
    status = load_status(task_id)
    if not status:
        return jsonify({'error': 'Task not found'}), 404

    # If completed, include results
    if status['status'] == 'completed':
        results = load_results(task_id)
        if results:
            status['results'] = results

    return jsonify(status)

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """Get full results"""
    results = load_results(task_id)
    if not results:
        return jsonify({'error': 'Results not found'}), 404

    return jsonify(results)

@app.route('/api/debug/tasks')
def debug_tasks():
    """Debug endpoint to list all tasks"""
    tasks = []
    if STATUS_DIR.exists():
        for status_file in STATUS_DIR.glob('*.json'):
            try:
                with open(status_file, 'r') as f:
                    status_data = json.load(f)
                    tasks.append({
                        'task_id': status_file.stem,
                        'status': status_data.get('status', 'unknown'),
                        'message': status_data.get('message', ''),
                        'progress': status_data.get('progress', 0),
                        'started_at': status_data.get('started_at', '')
                    })
            except Exception as e:
                tasks.append({
                    'task_id': status_file.stem,
                    'status': 'error',
                    'message': f'Failed to read status: {e}'
                })

    return jsonify({
        'total_tasks': len(tasks),
        'tasks': tasks
    })

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

def process_document_async(task_id, input_path, is_url):
    """Process document in background thread"""
    try:
        # Update status
        update_status(task_id, progress=10, message='Initializing analysis...')

        # Add timeout for URL processing
        if is_url:
            update_status(task_id, progress=20, message='Downloading document...')

        # Process document with timeout handling
        update_status(task_id, progress=30, message='Extracting content...')

        # Process document with timeout and OCR handling
        try:
            if is_url:
                # Use timeout for URL processing and disable OCR to prevent hanging
                import part_1
                original_ocr = part_1.OCR_AVAILABLE
                part_1.OCR_AVAILABLE = False

                try:
                    # Use timeout wrapper for URL processing
                    result = timeout_handler(platform.process_document, (input_path, is_url), 300)
                finally:
                    # Restore OCR setting
                    part_1.OCR_AVAILABLE = original_ocr
            else:
                # Local file processing with shorter timeout
                result = timeout_handler(platform.process_document, (input_path, is_url), 600)

        except TimeoutError as e:
            update_status(task_id, status='error', message=str(e))
            return
        except Exception as e:
            update_status(task_id, status='error', message=f'Processing error: {str(e)}')
            return

        if 'error' in result:
            update_status(task_id, status='error', message=result['error'])
            return

        # Update progress
        update_status(task_id, progress=80, message='Finalizing analysis...')

        # Store results
        save_results(task_id, result)

        # Mark as completed
        update_status(task_id,
                     status='completed',
                     progress=100,
                     message='Analysis completed successfully!',
                     completed_at=datetime.now().isoformat())

        # Clean up temporary file if it was uploaded
        if not is_url and os.path.exists(input_path):
            try:
                os.remove(input_path)
            except:
                pass

    except Exception as e:
        error_msg = f'Error: {str(e)}'
        print(f"Processing error for task {task_id}: {error_msg}")  # Debug log
        update_status(task_id, status='error', message=error_msg)

        # Clean up temporary file on error
        if not is_url and os.path.exists(input_path):
            try:
                os.remove(input_path)
            except:
                pass

if __name__ == '__main__':
    # Create storage directories
    os.makedirs('storage/status', exist_ok=True)
    os.makedirs('storage/results', exist_ok=True)

    print("🚀 Starting SciML Platform Web Application...")
    print("📱 Access the application at: http://localhost:5000")
    print("🔧 Health check available at: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5000, debug=True)
