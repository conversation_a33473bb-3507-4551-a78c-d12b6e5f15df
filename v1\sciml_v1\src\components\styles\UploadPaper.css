/* UploadPaper.css - Styles for the UploadPaper component */

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Upload container hover effect */
.upload-container {
  transition: all 0.3s ease;
}

.upload-container:hover {
  border-color: var(--highlight-color, #00b5ad);
  background-color: rgba(0, 181, 173, 0.05);
}

/* Progress bar animation */
@keyframes progress-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 181, 173, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0);
  }
}

.progress-bar-animate {
  animation: progress-pulse 2s infinite;
}

/* File icon animation */
@keyframes file-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.file-icon-animate {
  animation: file-bounce 2s ease-in-out infinite;
}

/* Tab hover effect */
.tab-hover {
  position: relative;
  overflow: hidden;
}

.tab-hover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--highlight-color, #00b5ad);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.tab-hover:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Button glow effect */
.btn-glow:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.6);
}
