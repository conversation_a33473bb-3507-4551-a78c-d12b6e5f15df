import { useState } from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../../DashboardLayout';

const Reports = () => {
  const [activeReport, setActiveReport] = useState('usage');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Mock data for reports
  const mockReports = {
    usage: {
      title: 'Usage Report',
      period: 'Last 30 Days',
      metrics: [
        { label: 'Total API Calls', value: '1,234,567', change: '+12.5%' },
        { label: 'Active Users', value: '8,432', change: '+8.3%' },
        { label: 'Storage Used', value: '2.4 TB', change: '+15.2%' },
        { label: 'Average Session', value: '24m 15s', change: '+5.7%' }
      ],
      chartData: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [
          {
            label: 'API Calls',
            data: [250000, 320000, 280000, 384567],
            color: 'bg-blue-500'
          },
          {
            label: 'Active Users',
            data: [1800, 2200, 2100, 2332],
            color: 'bg-green-500'
          }
        ]
      }
    },
    performance: {
      title: 'Performance Report',
      period: 'Last 30 Days',
      metrics: [
        { label: 'Avg Response Time', value: '245ms', change: '-8.2%' },
        { label: 'Success Rate', value: '99.8%', change: '+0.3%' },
        { label: 'Error Rate', value: '0.2%', change: '-0.1%' },
        { label: 'Uptime', value: '99.99%', change: '+0.01%' }
      ],
      chartData: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [
          {
            label: 'Response Time',
            data: [280, 265, 250, 245],
            color: 'bg-purple-500'
          },
          {
            label: 'Success Rate',
            data: [99.5, 99.6, 99.7, 99.8],
            color: 'bg-green-500'
          }
        ]
      }
    },
    security: {
      title: 'Security Report',
      period: 'Last 30 Days',
      metrics: [
        { label: 'Failed Logins', value: '234', change: '-15.3%' },
        { label: 'Blocked IPs', value: '45', change: '-8.7%' },
        { label: 'Security Alerts', value: '12', change: '-25.0%' },
        { label: 'Data Access', value: '98.5%', change: '+2.1%' }
      ],
      chartData: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [
          {
            label: 'Failed Logins',
            data: [80, 65, 45, 44],
            color: 'bg-red-500'
          },
          {
            label: 'Security Alerts',
            data: [5, 4, 2, 1],
            color: 'bg-yellow-500'
          }
        ]
      }
    }
  };

  const currentReport = mockReports[activeReport];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-2xl lg:text-3xl font-bold text-accent">Analytics Reports</h1>
          <p className="mt-2 text-primary">Comprehensive reports and insights</p>
        </motion.div>

        {/* Report Type Selector */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 mb-8 p-4">
          <div className="flex flex-wrap gap-2">
            {Object.keys(mockReports).map((report) => (
              <button
                key={report}
                onClick={() => setActiveReport(report)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  activeReport === report
                    ? 'bg-highlight text-white'
                    : 'bg-gray-100 text-primary hover:bg-gray-200'
                }`}
              >
                {mockReports[report].title}
              </button>
            ))}
          </div>
        </div>

        {/* Report Overview */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {currentReport.metrics.map((metric, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <p className="text-sm text-primary mb-2">{metric.label}</p>
              <div className="flex items-baseline justify-between">
                <p className="text-2xl font-bold text-accent">{metric.value}</p>
                <span className={`text-sm font-medium ${
                  metric.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
                }`}>
                  {metric.change}
                </span>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Report Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {currentReport.chartData.datasets.map((dataset, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
              variants={itemVariants}
            >
              <h2 className="text-lg font-semibold text-accent mb-6">{dataset.label} Trend</h2>
              <div className="h-64 flex items-end space-x-2">
                {dataset.data.map((value, i) => (
                  <div
                    key={i}
                    className="flex-1 bg-gray-100 rounded-t-lg hover:bg-gray-200 transition-colors duration-200"
                    style={{ height: `${(value / Math.max(...dataset.data)) * 100}%` }}
                  >
                    <div
                      className={`h-full ${dataset.color} rounded-t-lg`}
                      style={{ height: `${(value / Math.max(...dataset.data)) * 100}%` }}
                    ></div>
                  </div>
                ))}
              </div>
              <div className="mt-4 flex justify-between text-xs text-primary">
                {currentReport.chartData.labels.map((label, i) => (
                  <span key={i}>{label}</span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Report Actions */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100"
          variants={itemVariants}
        >
          <div className="p-6">
            <h2 className="text-lg font-semibold text-accent mb-6">Report Actions</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { label: 'Download PDF', icon: 'M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
                { label: 'Export CSV', icon: 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4' },
                { label: 'Share Report', icon: 'M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z' },
                { label: 'Schedule Export', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' }
              ].map((action, index) => (
                <button
                  key={index}
                  className="flex items-center justify-center space-x-2 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={action.icon} />
                  </svg>
                  <span className="text-sm font-medium text-primary">{action.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default Reports; 