import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const AllModels = () => {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [selectedModels, setSelectedModels] = useState([]);

  // Mock data for models
  useEffect(() => {
    const mockModels = [
      {
        id: 1,
        name: 'Climate_CNN_v1',
        type: 'CNN',
        status: 'deployed',
        accuracy: 94.2,
        paperId: 1,
        paperTitle: 'Deep Learning Approaches for Climate Modeling',
        createdDate: '2024-01-15T14:30:00Z',
        lastUpdated: '2024-01-15T16:45:00Z',
        version: '1.0.0',
        framework: 'TensorFlow',
        size: '45.2 MB',
        predictions: 1247,
        apiCalls: 892,
        tags: ['Climate', 'CNN', 'Temperature'],
        description: 'Convolutional neural network for climate pattern prediction',
        metrics: {
          precision: 0.942,
          recall: 0.938,
          f1Score: 0.940,
          mse: 0.0234
        },
        deployment: {
          endpoint: 'https://api.sciml.com/v1/models/climate-cnn-v1',
          instances: 3,
          avgResponseTime: '120ms',
          uptime: '99.8%'
        }
      },
      {
        id: 2,
        name: 'ProteinFold_Transformer',
        type: 'Transformer',
        status: 'training',
        accuracy: 91.8,
        paperId: 3,
        paperTitle: 'Machine Learning for Protein Folding Prediction',
        createdDate: '2024-01-13T16:45:00Z',
        lastUpdated: '2024-01-14T10:20:00Z',
        version: '2.1.0',
        framework: 'PyTorch',
        size: '128.7 MB',
        predictions: 0,
        apiCalls: 0,
        tags: ['Protein', 'Transformer', 'Bioinformatics'],
        description: 'Transformer model for protein secondary structure prediction',
        metrics: {
          precision: 0.918,
          recall: 0.915,
          f1Score: 0.916,
          mse: 0.0187
        },
        training: {
          progress: 78,
          epoch: 156,
          totalEpochs: 200,
          eta: '2h 15m'
        }
      },
      {
        id: 3,
        name: 'MedImage_ResNet',
        type: 'ResNet',
        status: 'deployed',
        accuracy: 96.7,
        paperId: 5,
        paperTitle: 'Computer Vision for Medical Image Analysis',
        createdDate: '2024-01-11T18:20:00Z',
        lastUpdated: '2024-01-12T09:30:00Z',
        version: '1.2.1',
        framework: 'TensorFlow',
        size: '89.4 MB',
        predictions: 3456,
        apiCalls: 2341,
        tags: ['Medical', 'ResNet', 'Computer Vision'],
        description: 'ResNet architecture for medical image classification',
        metrics: {
          precision: 0.967,
          recall: 0.964,
          f1Score: 0.965,
          mse: 0.0098
        },
        deployment: {
          endpoint: 'https://api.sciml.com/v1/models/medimage-resnet',
          instances: 5,
          avgResponseTime: '95ms',
          uptime: '99.9%'
        }
      },
      {
        id: 4,
        name: 'SciBERT_Classifier',
        type: 'BERT',
        status: 'testing',
        accuracy: 89.3,
        paperId: 8,
        paperTitle: 'Natural Language Processing for Scientific Literature',
        createdDate: '2024-01-10T20:15:00Z',
        lastUpdated: '2024-01-11T14:22:00Z',
        version: '1.0.0-beta',
        framework: 'Hugging Face',
        size: '412.8 MB',
        predictions: 234,
        apiCalls: 156,
        tags: ['NLP', 'BERT', 'Classification'],
        description: 'BERT model fine-tuned for scientific text classification',
        metrics: {
          precision: 0.893,
          recall: 0.891,
          f1Score: 0.892,
          mse: 0.0456
        },
        testing: {
          testsPassed: 87,
          totalTests: 100,
          coverage: '94%',
          issues: 2
        }
      },
      {
        id: 5,
        name: 'Quantum_GAN',
        type: 'GAN',
        status: 'failed',
        accuracy: 0,
        paperId: 2,
        paperTitle: 'Quantum Computing Applications in Drug Discovery',
        createdDate: '2024-01-14T10:30:00Z',
        lastUpdated: '2024-01-14T12:45:00Z',
        version: '0.1.0',
        framework: 'Qiskit',
        size: '0 MB',
        predictions: 0,
        apiCalls: 0,
        tags: ['Quantum', 'GAN', 'Drug Discovery'],
        description: 'Quantum generative adversarial network for molecular generation',
        error: {
          message: 'Quantum circuit compilation failed',
          code: 'QC_COMPILE_ERROR',
          timestamp: '2024-01-14T12:45:23Z'
        }
      }
    ];

    setTimeout(() => {
      setModels(mockModels);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort models
  const filteredModels = models
    .filter(model => {
      const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           model.paperTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           model.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesStatus = filterStatus === 'all' || model.status === filterStatus;
      const matchesType = filterType === 'all' || model.type === filterType;
      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdDate) - new Date(a.createdDate);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'accuracy':
          return b.accuracy - a.accuracy;
        case 'predictions':
          return b.predictions - a.predictions;
        default:
          return 0;
      }
    });

  const getStatusColor = (status) => {
    switch (status) {
      case 'deployed':
        return 'bg-green-100 text-green-800';
      case 'training':
        return 'bg-blue-100 text-blue-800';
      case 'testing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'deployed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'training':
        return (
          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'testing':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const handleSelectModel = (modelId) => {
    setSelectedModels(prev =>
      prev.includes(modelId)
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  const handleSelectAll = () => {
    if (selectedModels.length === filteredModels.length) {
      setSelectedModels([]);
    } else {
      setSelectedModels(filteredModels.map(model => model.id));
    }
  };

  return (
    <DashboardLayout>
      <div className="pt-16 sm:pt-20 px-4 lg:px-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">All Models</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Manage and monitor your AI models across all papers
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/models/create"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Create Model
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search models, papers, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="deployed">Deployed</option>
                <option value="training">Training</option>
                <option value="testing">Testing</option>
                <option value="failed">Failed</option>
              </select>

              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="all">All Types</option>
                <option value="CNN">CNN</option>
                <option value="Transformer">Transformer</option>
                <option value="ResNet">ResNet</option>
                <option value="BERT">BERT</option>
                <option value="GAN">GAN</option>
              </select>

              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Date</option>
                <option value="name">Sort by Name</option>
                <option value="accuracy">Sort by Accuracy</option>
                <option value="predictions">Sort by Predictions</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Models List */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading models...</p>
            </div>
          ) : filteredModels.length === 0 ? (
            <div className="p-8 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No models found</h3>
              <p className="mt-2 text-primary">
                {searchQuery || filterStatus !== 'all' || filterType !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Upload papers to automatically generate AI models'
                }
              </p>
              {!searchQuery && filterStatus === 'all' && filterType === 'all' && (
                <Link
                  to="/upload-paper"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Upload Paper
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="bg-gray-50 px-4 lg:px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded"
                    checked={selectedModels.length === filteredModels.length}
                    onChange={handleSelectAll}
                  />
                  <span className="ml-3 text-sm font-medium text-accent">
                    {selectedModels.length > 0 ? `${selectedModels.length} selected` : 'Select all'}
                  </span>
                  {selectedModels.length > 0 && (
                    <div className="ml-4 flex space-x-2">
                      <button className="text-sm text-green-600 hover:text-green-800">Deploy</button>
                      <button className="text-sm text-blue-600 hover:text-blue-800">Export</button>
                      <button className="text-sm text-red-600 hover:text-red-800">Delete</button>
                    </div>
                  )}
                </div>
              </div>

              {/* Table Content */}
              <div className="divide-y divide-gray-200">
                <AnimatePresence>
                  {filteredModels.map((model, index) => (
                    <motion.div
                      key={model.id}
                      className="p-4 lg:p-6 hover:bg-gray-50 transition-colors duration-200"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <div className="flex items-start space-x-4">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded mt-1"
                          checked={selectedModels.includes(model.id)}
                          onChange={() => handleSelectModel(model.id)}
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3">
                                <h3 className="text-lg font-semibold text-accent hover:text-highlight transition-colors duration-200">
                                  <Link to={`/dashboard/models/${model.id}`}>
                                    {model.name}
                                  </Link>
                                </h3>
                                <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-accent rounded-full">
                                  {model.type}
                                </span>
                                <span className="text-xs text-primary">v{model.version}</span>
                              </div>
                              <p className="text-sm text-primary mt-1">
                                From: <Link to={`/dashboard/papers/${model.paperId}`} className="text-highlight hover:text-accent">
                                  {model.paperTitle}
                                </Link>
                              </p>
                              <p className="text-xs text-gray-500 mt-1">{model.description}</p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {model.tags.map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>

                            <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(model.status)}`}>
                                {getStatusIcon(model.status)}
                                <span className="ml-1 capitalize">{model.status}</span>
                              </div>
                              <div className="text-xs text-primary">
                                Created: {new Date(model.createdDate).toLocaleDateString()}
                              </div>
                              {model.accuracy > 0 && (
                                <div className="text-xs font-medium text-highlight">
                                  {model.accuracy}% Accuracy
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Model Metrics */}
                          <div className="mt-4 grid grid-cols-2 lg:grid-cols-6 gap-4 text-sm">
                            <div>
                              <span className="text-primary">Framework:</span>
                              <span className="ml-1 font-medium text-accent">{model.framework}</span>
                            </div>
                            <div>
                              <span className="text-primary">Size:</span>
                              <span className="ml-1 font-medium text-accent">{model.size}</span>
                            </div>
                            <div>
                              <span className="text-primary">Predictions:</span>
                              <span className="ml-1 font-medium text-accent">{model.predictions.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-primary">API Calls:</span>
                              <span className="ml-1 font-medium text-accent">{model.apiCalls.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-primary">Precision:</span>
                              <span className="ml-1 font-medium text-accent">
                                {model.metrics ? (model.metrics.precision * 100).toFixed(1) + '%' : 'N/A'}
                              </span>
                            </div>
                            <div>
                              <span className="text-primary">F1 Score:</span>
                              <span className="ml-1 font-medium text-accent">
                                {model.metrics ? (model.metrics.f1Score * 100).toFixed(1) + '%' : 'N/A'}
                              </span>
                            </div>
                          </div>

                          {/* Status-specific Information */}
                          {model.status === 'training' && model.training && (
                            <div className="mt-4 bg-blue-50 rounded-lg p-3">
                              <div className="flex justify-between items-center mb-2">
                                <span className="text-sm font-medium text-blue-800">Training Progress</span>
                                <span className="text-sm text-blue-600">{model.training.progress}%</span>
                              </div>
                              <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${model.training.progress}%` }}
                                />
                              </div>
                              <div className="text-xs text-blue-600">
                                Epoch {model.training.epoch}/{model.training.totalEpochs} • ETA: {model.training.eta}
                              </div>
                            </div>
                          )}

                          {model.status === 'deployed' && model.deployment && (
                            <div className="mt-4 bg-green-50 rounded-lg p-3">
                              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 text-xs">
                                <div>
                                  <span className="text-green-600">Instances:</span>
                                  <span className="ml-1 font-medium text-green-800">{model.deployment.instances}</span>
                                </div>
                                <div>
                                  <span className="text-green-600">Response Time:</span>
                                  <span className="ml-1 font-medium text-green-800">{model.deployment.avgResponseTime}</span>
                                </div>
                                <div>
                                  <span className="text-green-600">Uptime:</span>
                                  <span className="ml-1 font-medium text-green-800">{model.deployment.uptime}</span>
                                </div>
                                <div>
                                  <span className="text-green-600">Endpoint:</span>
                                  <span className="ml-1 font-medium text-green-800 truncate">Active</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {model.status === 'failed' && model.error && (
                            <div className="mt-4 bg-red-50 rounded-lg p-3">
                              <div className="text-sm font-medium text-red-800 mb-1">Error: {model.error.code}</div>
                              <div className="text-xs text-red-600">{model.error.message}</div>
                              <div className="text-xs text-red-500 mt-1">
                                {new Date(model.error.timestamp).toLocaleString()}
                              </div>
                            </div>
                          )}

                          <div className="mt-4 flex flex-wrap gap-2">
                            <Link
                              to={`/dashboard/models/${model.id}`}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                            >
                              View Details
                            </Link>
                            {model.status === 'deployed' && (
                              <>
                                <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                                  API Docs
                                </button>
                                <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                                  Monitor
                                </button>
                              </>
                            )}
                            {model.status === 'training' && (
                              <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 hover:text-yellow-800 transition-colors duration-200">
                                Stop Training
                              </button>
                            )}
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                              Download
                            </button>
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default AllModels;
