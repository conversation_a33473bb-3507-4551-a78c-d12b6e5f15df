import { useState } from 'react';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/APIs.css';

const APIs = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedEndpoint, setCopiedEndpoint] = useState(null);
  const [expandedAPI, setExpandedAPI] = useState(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Sample API data
  const apiData = {
    apiKey: 'sk_test_51Hb9xjKG8MJd29384nKwEFvhZLX',
    endpoints: [
      {
        id: 1,
        name: 'Paper Analysis',
        endpoint: '/api/v1/papers/analyze',
        description: 'Extract key information, figures, and data from research papers',
        method: 'POST',
        parameters: [
          { name: 'paper_url', type: 'string', required: true, description: 'URL to the paper PDF' },
          { name: 'extract_figures', type: 'boolean', required: false, description: 'Whether to extract figures from the paper' },
          { name: 'extract_tables', type: 'boolean', required: false, description: 'Whether to extract tables from the paper' }
        ],
        response: {
          title: 'string',
          authors: 'array',
          abstract: 'string',
          keywords: 'array',
          figures: 'array (if requested)',
          tables: 'array (if requested)',
          references: 'array'
        },
        example: `curl -X POST https://api.sciml.ai/v1/papers/analyze \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "paper_url": "https://arxiv.org/pdf/2106.12423.pdf",
    "extract_figures": true,
    "extract_tables": true
  }'`
      },
      {
        id: 2,
        name: 'Model Generation',
        endpoint: '/api/v1/models/generate',
        description: 'Generate machine learning models from paper data',
        method: 'POST',
        parameters: [
          { name: 'paper_id', type: 'string', required: true, description: 'ID of the analyzed paper' },
          { name: 'model_type', type: 'string', required: true, description: 'Type of model to generate (regression, classification, etc.)' },
          { name: 'target_variable', type: 'string', required: true, description: 'Target variable for the model' }
        ],
        response: {
          model_id: 'string',
          model_type: 'string',
          accuracy: 'number',
          parameters: 'object',
          download_url: 'string'
        },
        example: `curl -X POST https://api.sciml.ai/v1/models/generate \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "paper_id": "paper_12345",
    "model_type": "regression",
    "target_variable": "temperature"
  }'`
      },
      {
        id: 3,
        name: 'Model Prediction',
        endpoint: '/api/v1/models/predict',
        description: 'Make predictions using generated models',
        method: 'POST',
        parameters: [
          { name: 'model_id', type: 'string', required: true, description: 'ID of the generated model' },
          { name: 'input_data', type: 'object', required: true, description: 'Input data for prediction' }
        ],
        response: {
          prediction: 'number or array',
          confidence: 'number',
          explanation: 'object'
        },
        example: `curl -X POST https://api.sciml.ai/v1/models/predict \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model_id": "model_67890",
    "input_data": {
      "feature1": 0.5,
      "feature2": 1.2,
      "feature3": -0.7
    }
  }'`
      }
    ]
  };

  // API documentation tabs
  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'authentication', label: 'Authentication' },
    { id: 'endpoints', label: 'Endpoints' },
    { id: 'examples', label: 'Examples' }
  ];

  // Handle copy to clipboard
  const handleCopyEndpoint = (endpoint) => {
    navigator.clipboard.writeText(endpoint);
    setCopiedEndpoint(endpoint);
    setTimeout(() => setCopiedEndpoint(null), 2000);
  };

  // Handle API expansion
  const toggleAPIExpansion = (id) => {
    if (expandedAPI === id) {
      setExpandedAPI(null);
    } else {
      setExpandedAPI(id);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow py-12 lg:py-20 px-4 bg-white">
        {/* Decorative elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-highlight opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-primary opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-6 lg:mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl sm:text-4xl font-bold text-accent">
              SciML <span className="text-highlight">API Platform</span>
            </h1>
            <p className="mt-3 lg:mt-4 text-base lg:text-lg text-primary max-w-2xl mx-auto">
              Transform your research papers into powerful APIs. Access AI-generated models and tools through our platform.
            </p>
          </motion.div>

          {/* API documentation tabs */}
          <div className="mb-6 lg:mb-8 border-b border-gray-200 overflow-x-auto">
            <div className="flex -mb-px min-w-max">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  className={`mr-2 inline-block py-3 lg:py-4 px-3 lg:px-4 text-xs lg:text-sm font-medium focus:outline-none whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'text-highlight border-b-2 border-highlight'
                      : 'text-accent hover:text-highlight border-b-2 border-transparent'
                  }`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* API documentation content */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Overview section */}
            {activeTab === 'overview' && (
              <motion.div
                className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100 mb-6 lg:mb-8"
                variants={itemVariants}
              >
                <h2 className="text-xl lg:text-2xl font-semibold text-accent mb-3 lg:mb-4">How Our API Platform Works</h2>
                <p className="text-sm lg:text-base text-primary mb-4">
                  The SciML API platform transforms your research papers into interactive tools and APIs. Here's what you can do:
                </p>

                {/* Feature grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6 mb-6">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-highlight bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-sm lg:text-base font-medium text-accent">Paper Upload & Analysis</h3>
                      <p className="text-xs lg:text-sm text-primary">Upload research papers and get AI-powered analysis</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-highlight bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-sm lg:text-base font-medium text-accent">AI Model Generation</h3>
                      <p className="text-xs lg:text-sm text-primary">Automatically generate ML models from your research</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-highlight bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-sm lg:text-base font-medium text-accent">API Integration</h3>
                      <p className="text-xs lg:text-sm text-primary">Access generated models through RESTful APIs</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-highlight bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-sm lg:text-base font-medium text-accent">Real-time Analytics</h3>
                      <p className="text-xs lg:text-sm text-primary">Monitor usage and performance metrics</p>
                    </div>
                  </div>
                </div>

                <p className="text-sm lg:text-base text-primary mb-4">
                  Our API is RESTful and uses standard HTTP response codes, authentication, and verbs. All responses are returned in JSON format.
                </p>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 mt-4 lg:mt-6">
                  <button className="px-4 py-2 bg-highlight text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 text-sm lg:text-base">
                    Get API Key
                  </button>
                  <button className="px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-300 text-sm lg:text-base">
                    View Examples
                  </button>
                </div>
              </motion.div>
            )}

            {/* Authentication section */}
            {activeTab === 'authentication' && (
              <motion.div
                className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100 mb-6 lg:mb-8"
                variants={itemVariants}
              >
                <h2 className="text-xl lg:text-2xl font-semibold text-accent mb-3 lg:mb-4">Authentication</h2>
                <p className="text-sm lg:text-base text-primary mb-4">
                  The SciML API uses API keys for authentication. You can view and manage your API keys in your dashboard.
                </p>
                <div className="bg-gray-100 p-3 lg:p-4 rounded-lg mb-4 lg:mb-6">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-2 space-y-2 sm:space-y-0">
                    <span className="text-accent text-sm lg:text-base">Your API Key:</span>
                    <button className="text-highlight hover:text-accent transition-colors duration-200 text-xs lg:text-sm self-start sm:self-auto">
                      Generate New Key
                    </button>
                  </div>
                  <div className="flex items-center">
                    <code className="bg-gray-200 text-primary p-2 rounded flex-grow font-mono text-xs lg:text-sm overflow-hidden">
                      {apiData.apiKey.substring(0, 8)}...{apiData.apiKey.substring(apiData.apiKey.length - 4)}
                    </code>
                    <button
                      className="ml-2 text-highlight hover:text-accent transition-colors duration-200 flex-shrink-0"
                      onClick={() => handleCopyEndpoint(apiData.apiKey)}
                    >
                      {copiedEndpoint === apiData.apiKey ? (
                        <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      ) : (
                        <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <p className="text-sm lg:text-base text-primary mb-4">
                  Authentication to the API is performed via HTTP Bearer Auth. Provide your API key as the bearer token value in the Authorization header:
                </p>
                <div className="bg-gray-100 p-3 lg:p-4 rounded-lg mb-4 lg:mb-6 overflow-x-auto">
                  <code className="text-primary font-mono text-xs lg:text-sm whitespace-nowrap">
                    Authorization: Bearer YOUR_API_KEY
                  </code>
                </div>
                <div className="bg-yellow-100 border border-yellow-300 rounded-md p-4 mb-6">
                  <div className="flex">
                    <svg className="w-6 h-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <div>
                      <h3 className="text-yellow-700 font-medium mb-1">Security Warning</h3>
                      <p className="text-primary text-sm">
                        Keep your API keys secure and never share them in publicly accessible areas such as GitHub, client-side code, or blog posts.
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Endpoints section */}
            {activeTab === 'endpoints' && (
              <motion.div
                className="space-y-6"
                variants={itemVariants}
              >
                {apiData.endpoints.map(api => (
                  <div
                    key={api.id}
                    className="bg-white rounded-xl p-6 shadow-xl border border-gray-100"
                  >
                    <div
                      className="flex justify-between items-center cursor-pointer"
                      onClick={() => toggleAPIExpansion(api.id)}
                    >
                      <div>
                        <h3 className="text-xl font-semibold text-accent">{api.name}</h3>
                        <p className="text-primary mt-1">{api.description}</p>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          api.method === 'GET' ? 'bg-green-500' :
                          api.method === 'POST' ? 'bg-blue-500' :
                          api.method === 'PUT' ? 'bg-yellow-500' :
                          api.method === 'DELETE' ? 'bg-red-500' : 'bg-gray-500'
                        } text-white mr-3`}>
                          {api.method}
                        </span>
                        <svg
                          className={`w-5 h-5 text-primary transition-transform duration-300 ${expandedAPI === api.id ? 'transform rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                    </div>

                    <div className="flex items-center mt-4">
                      <code className="bg-gray-100 text-primary p-2 rounded flex-grow font-mono text-sm">
                        {api.endpoint}
                      </code>
                      <button
                        className="ml-2 text-highlight hover:text-accent transition-colors duration-200"
                        onClick={() => handleCopyEndpoint(api.endpoint)}
                      >
                        {copiedEndpoint === api.endpoint ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                          </svg>
                        )}
                      </button>
                    </div>

                    {expandedAPI === api.id && (
                      <div className="mt-6 border-t border-gray-200 pt-6">
                        <div className="mb-6">
                          <h4 className="text-accent font-medium mb-2">Parameters</h4>
                          <table className="min-w-full">
                            <thead>
                              <tr>
                                <th className="text-left text-xs font-medium text-accent uppercase tracking-wider pb-2">Name</th>
                                <th className="text-left text-xs font-medium text-accent uppercase tracking-wider pb-2">Type</th>
                                <th className="text-left text-xs font-medium text-accent uppercase tracking-wider pb-2">Required</th>
                                <th className="text-left text-xs font-medium text-accent uppercase tracking-wider pb-2">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              {api.parameters.map((param, index) => (
                                <tr key={index} className="border-t border-gray-200">
                                  <td className="py-2 text-primary font-mono text-sm">{param.name}</td>
                                  <td className="py-2 text-primary text-sm">{param.type}</td>
                                  <td className="py-2 text-primary text-sm">{param.required ? 'Yes' : 'No'}</td>
                                  <td className="py-2 text-primary text-sm">{param.description}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        <div className="mb-6">
                          <h4 className="text-accent font-medium mb-2">Response</h4>
                          <div className="bg-gray-100 p-4 rounded-lg">
                            <pre className="text-primary font-mono text-sm whitespace-pre-wrap">
                              {JSON.stringify(api.response, null, 2)}
                            </pre>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-accent font-medium mb-2">Example</h4>
                          <div className="bg-gray-100 p-4 rounded-lg">
                            <pre className="text-primary font-mono text-sm whitespace-pre-wrap overflow-x-auto">
                              {api.example}
                            </pre>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </motion.div>
            )}

            {/* Examples section */}
            {activeTab === 'examples' && (
              <motion.div
                className="space-y-6 lg:space-y-8"
                variants={itemVariants}
              >
                {/* JavaScript Example */}
                <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100">
                  <h3 className="text-lg lg:text-xl font-semibold text-accent mb-3 lg:mb-4">JavaScript Example</h3>
                  <p className="text-sm lg:text-base text-primary mb-4">Upload a paper and get AI analysis using JavaScript:</p>
                  <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre className="text-green-400 font-mono text-xs lg:text-sm">
{`// Upload and analyze a research paper
const uploadPaper = async (file) => {
  const formData = new FormData();
  formData.append('paper', file);

  const response = await fetch('https://api.sciml.com/v1/papers/upload', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: formData
  });

  const result = await response.json();
  console.log('Paper analysis:', result);
  return result;
};

// Get generated model predictions
const getPrediction = async (modelId, inputData) => {
  const response = await fetch(\`https://api.sciml.com/v1/models/\${modelId}/predict\`, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ input: inputData })
  });

  return await response.json();
};`}
                    </pre>
                  </div>
                </div>

                {/* Python Example */}
                <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100">
                  <h3 className="text-lg lg:text-xl font-semibold text-accent mb-3 lg:mb-4">Python Example</h3>
                  <p className="text-sm lg:text-base text-primary mb-4">Use Python to interact with SciML APIs:</p>
                  <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre className="text-green-400 font-mono text-xs lg:text-sm">
{`import requests
import json

# SciML API client
class SciMLClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.sciml.com/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def upload_paper(self, file_path):
        """Upload a research paper for analysis"""
        with open(file_path, 'rb') as f:
            files = {'paper': f}
            response = requests.post(
                f"{self.base_url}/papers/upload",
                files=files,
                headers={"Authorization": f"Bearer {self.api_key}"}
            )
        return response.json()

    def get_model_prediction(self, model_id, input_data):
        """Get predictions from a generated model"""
        response = requests.post(
            f"{self.base_url}/models/{model_id}/predict",
            headers=self.headers,
            json={"input": input_data}
        )
        return response.json()

# Usage example
client = SciMLClient("your_api_key_here")
result = client.upload_paper("research_paper.pdf")
print(f"Analysis complete: {result['status']}")`}
                    </pre>
                  </div>
                </div>

                {/* cURL Example */}
                <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100">
                  <h3 className="text-lg lg:text-xl font-semibold text-accent mb-3 lg:mb-4">cURL Example</h3>
                  <p className="text-sm lg:text-base text-primary mb-4">Quick API testing with cURL commands:</p>
                  <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre className="text-green-400 font-mono text-xs lg:text-sm">
{`# Upload a research paper
curl -X POST "https://api.sciml.com/v1/papers/upload" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -F "paper=@research_paper.pdf"

# Get paper analysis status
curl -X GET "https://api.sciml.com/v1/papers/12345/status" \\
  -H "Authorization: Bearer YOUR_API_KEY"

# Make a model prediction
curl -X POST "https://api.sciml.com/v1/models/model_123/predict" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "input": {
      "temperature": 25.5,
      "pressure": 1.2,
      "concentration": 0.8
    }
  }'

# List all your papers
curl -X GET "https://api.sciml.com/v1/papers" \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
                    </pre>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default APIs;
