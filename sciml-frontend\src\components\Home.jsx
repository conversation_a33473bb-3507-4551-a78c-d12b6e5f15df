import { useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/Home.css';

const Home = () => {
  // State for particle animation
  const [particles] = useState(Array.from({ length: 20 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 4 + 1,
    duration: Math.random() * 20 + 10
  })));

  // Animation for sections when they come into view
  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // Enhanced animations are applied directly in the components

  // Hero section animations
  const heroControls = useAnimation();
  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // Features section animations
  const featuresControls = useAnimation();
  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // How it works section animations
  const howItWorksControls = useAnimation();
  const [howItWorksRef, howItWorksInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // Testimonials section animations
  const testimonialsControls = useAnimation();
  const [testimonialsRef, testimonialsInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // CTA section animations
  const ctaControls = useAnimation();
  const [ctaRef, ctaInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // Trigger animations when sections come into view
  useEffect(() => {
    if (heroInView) heroControls.start('visible');
    if (featuresInView) featuresControls.start('visible');
    if (howItWorksInView) howItWorksControls.start('visible');
    if (testimonialsInView) testimonialsControls.start('visible');
    if (ctaInView) ctaControls.start('visible');
  }, [heroInView, featuresInView, howItWorksInView, testimonialsInView, ctaInView,
      heroControls, featuresControls, howItWorksControls, testimonialsControls, ctaControls]);

  return (
    <div className="min-h-screen bg-background text-textColor flex flex-col">
      <Navbar />

      {/* Hero Section */}
      <motion.div
        ref={heroRef}
        initial="hidden"
        animate={heroControls}
        variants={fadeInUp}
        className="relative pt-32 pb-20 overflow-hidden"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-primary via-background to-white opacity-10"></div>
        <div className="absolute inset-0 bg-dots-pattern"></div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              className="absolute rounded-full bg-highlight"
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                width: `${particle.size}px`,
                height: `${particle.size}px`,
                opacity: 0.2
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0.1, 0.3, 0.1]
              }}
              transition={{
                duration: particle.duration,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        {/* Main content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:flex lg:items-center lg:justify-between">
            <div className="lg:w-1/2">
              <motion.div
                className="relative"
                variants={{
                  hidden: { opacity: 0 },
                  visible: { opacity: 1 }
                }}
              >
                {/* Decorative accent */}
                <motion.div
                  className="absolute -left-6 top-0 h-20 w-1 bg-gradient-to-b from-highlight to-transparent"
                  initial={{ scaleY: 0 }}
                  animate={{ scaleY: 1 }}
                  transition={{ duration: 1, delay: 0.5 }}
                ></motion.div>

                <motion.h1
                  className="text-4xl md:text-6xl font-bold leading-tight"
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: {
                      opacity: 1,
                      y: 0,
                      transition: { duration: 0.5, delay: 0.2 }
                    }
                  }}
                >
                  From Static Papers<br />
                  to <span className="text-transparent bg-clip-text bg-gradient-to-r from-highlight via-accent to-primary">Smart Science</span><br />
                  Tools
                </motion.h1>
              </motion.div>

              <motion.p
                className="mt-6 text-xl text-accent max-w-3xl font-light"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.5, delay: 0.3 }
                  }
                }}
              >
                SciML transforms published research into
                machine-readable APIs, agents, and models using advanced AI technology.
              </motion.p>

              <motion.div
                className="mt-10 flex flex-wrap gap-4"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.5, delay: 0.4 }
                  }
                }}
              >
                <motion.a
                  href="#upload-paper"
                  className="btn-futuristic px-8 py-3 bg-gradient-to-r from-highlight to-accent rounded-md text-base font-medium text-white inline-flex items-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 0 15px rgba(0, 181, 173, 0.5)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  Upload Your Paper
                  <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.a>

                <motion.a
                  href="#learn-more"
                  className="btn-futuristic px-8 py-3 bg-white bg-opacity-10 backdrop-blur-sm border border-highlight text-highlight hover:text-white rounded-md text-base font-medium transition-all duration-300"
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(0, 181, 173, 0.1)",
                    boxShadow: "0 0 15px rgba(0, 181, 173, 0.3)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  Learn More
                </motion.a>
              </motion.div>
            </div>

            <motion.div
              className="mt-10 lg:mt-0 lg:w-1/2"
              variants={{
                hidden: { opacity: 0, x: 50 },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    type: "spring",
                    stiffness: 100,
                    damping: 15,
                    delay: 0.3
                  }
                }
              }}
            >
              <div className="relative">
                {/* Animated decorative elements */}
                <motion.div
                  className="absolute -top-10 -right-10 w-60 h-60 bg-highlight opacity-10 rounded-full blur-3xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.1, 0.15, 0.1]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                ></motion.div>

                <motion.div
                  className="absolute -bottom-10 -left-10 w-60 h-60 bg-primary opacity-10 rounded-full blur-3xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.1, 0.15, 0.1]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                ></motion.div>

                <motion.div
                  className="relative z-10 glass-card p-2 rounded-xl overflow-hidden"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src="/hero_2.jpeg"
                    alt="Scientific visualization with AI"
                    className="w-full relative z-10 rounded-lg"
                  />

                  {/* Image overlay effect */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-primary via-transparent to-highlight opacity-20 rounded-xl"></div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Features Section */}
      <motion.div
        ref={featuresRef}
        initial="hidden"
        animate={featuresControls}
        variants={fadeInUp}
        className="py-20 relative overflow-hidden"
        id="features"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-white"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-20 right-10 w-72 h-72 bg-highlight opacity-5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.05, 0.08, 0.05]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { duration: 0.5 }
              }
            }}
          >
            <motion.div
              className="inline-block"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-3xl font-extrabold text-accent sm:text-4xl">
                Transform Research into <span className="text-transparent bg-clip-text bg-gradient-to-r from-highlight to-accent">Action</span>
              </h2>
            </motion.div>
            <p className="mt-4 max-w-2xl text-xl text-secondary mx-auto font-light">
              Our platform extracts insights, data, and models from scientific papers.
            </p>
          </motion.div>

          <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
            {/* Feature cards with staggered animation */}
            {[
              {
                title: "Extract Data",
                description: "Automatically extract tables, figures, and datasets from research papers.",
                icon: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
                gradient: "from-highlight to-accent",
                delay: 0.1
              },
              {
                title: "Build Models",
                description: "Recreate and run models described in papers with just a few clicks.",
                icon: "M13 10V3L4 14h7v7l9-11h-7z",
                gradient: "from-accent to-primary",
                delay: 0.2
              },
              {
                title: "Generate APIs",
                description: "Create ready-to-use APIs from research findings for easy integration.",
                icon: "M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
                gradient: "from-primary to-highlight",
                delay: 0.3
              }
            ].map((feature) => (
              <motion.div
                key={feature.title}
                className="feature-card glass-card rounded-xl px-6 py-8 shadow-lg border border-white border-opacity-20 overflow-hidden"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.5, delay: feature.delay }
                  }
                }}
                whileHover={{ y: -10, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
              >
                {/* Card background gradient */}
                <div className="absolute -inset-1 bg-gradient-to-r from-transparent via-highlight to-transparent opacity-10 blur-xl"></div>

                {/* Card content */}
                <div className="relative z-10">
                  <div className={`feature-icon h-14 w-14 rounded-xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center shadow-lg p-3`}>
                    <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={feature.icon} />
                    </svg>
                  </div>

                  <h3 className="mt-5 text-xl font-semibold text-accent">{feature.title}</h3>

                  <div className="mt-2 h-1 w-10 bg-gradient-to-r from-highlight to-transparent rounded-full"></div>

                  <p className="mt-4 text-base text-secondary">
                    {feature.description}
                  </p>

                  <motion.div
                    className="mt-6 text-highlight font-medium flex items-center"
                    initial={{ opacity: 0.8 }}
                    whileHover={{
                      opacity: 1,
                      x: 5
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    Learn more
                    <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* How It Works Section */}
      <motion.div
        ref={howItWorksRef}
        initial="hidden"
        animate={howItWorksControls}
        variants={fadeInUp}
        className="py-20 relative overflow-hidden"
        id="how-it-works"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-white to-background"></div>
        <div className="absolute inset-0 bg-dots-pattern opacity-5"></div>

        {/* Decorative elements */}
        <motion.div
          className="absolute bottom-20 left-10 w-80 h-80 bg-primary opacity-5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.05, 0.08, 0.05]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-20"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { duration: 0.5 }
              }
            }}
          >
            <motion.div
              className="inline-block"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-3xl font-extrabold text-accent sm:text-4xl">
                How <span className="text-transparent bg-clip-text bg-gradient-to-r from-highlight to-accent">SciML</span> Works
              </h2>
            </motion.div>
            <p className="mt-4 max-w-2xl text-xl text-secondary mx-auto font-light">
              A simple step-by-step process to transform your research papers
            </p>
          </motion.div>

          {/* Steps */}
          <div className="relative">
            {/* Connection line */}
            <div className="timeline-connector"></div>

            {/* Step 1 */}
            <motion.div
              className="relative z-10 mb-24 flex items-center"
              variants={{
                hidden: { opacity: 0, x: -50 },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: { duration: 0.5, delay: 0.2 }
                }
              }}
            >
              <div className="flex flex-col md:flex-row items-center">
                <motion.div
                  className="flex-shrink-0 bg-gradient-to-r from-highlight to-accent text-white text-2xl font-bold rounded-full h-20 w-20 flex items-center justify-center shadow-lg pulse-glow"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="relative z-10">1</span>
                  <div className="absolute inset-0 rounded-full bg-white opacity-20 blur-sm"></div>
                </motion.div>

                <motion.div
                  className="mt-4 md:mt-0 md:ml-8 glass-card p-6 rounded-xl shadow-lg max-w-2xl border border-white border-opacity-10"
                  whileHover={{
                    y: -5,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-highlight opacity-5 rounded-full blur-xl"></div>
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-highlight to-accent">Upload Your Research Paper</h3>
                    <div className="mt-2 h-0.5 w-10 bg-gradient-to-r from-highlight to-transparent rounded-full"></div>
                    <p className="mt-3 text-secondary">
                      Simply upload your PDF or provide a URL to your published paper. Our system accepts various formats and sources.
                    </p>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Step 2 */}
            <motion.div
              className="relative z-10 mb-24 flex items-center justify-end"
              variants={{
                hidden: { opacity: 0, x: 50 },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: { duration: 0.5, delay: 0.4 }
                }
              }}
            >
              <div className="flex flex-col md:flex-row-reverse items-center">
                <motion.div
                  className="flex-shrink-0 bg-gradient-to-r from-accent to-primary text-white text-2xl font-bold rounded-full h-20 w-20 flex items-center justify-center shadow-lg pulse-glow"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="relative z-10">2</span>
                  <div className="absolute inset-0 rounded-full bg-white opacity-20 blur-sm"></div>
                </motion.div>

                <motion.div
                  className="mt-4 md:mt-0 md:mr-8 glass-card p-6 rounded-xl shadow-lg max-w-2xl border border-white border-opacity-10"
                  whileHover={{
                    y: -5,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="absolute top-0 left-0 w-20 h-20 bg-accent opacity-5 rounded-full blur-xl"></div>
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-accent to-primary">AI Analysis & Extraction</h3>
                    <div className="mt-2 h-0.5 w-10 bg-gradient-to-r from-accent to-transparent rounded-full"></div>
                    <p className="mt-3 text-secondary">
                      Our advanced AI analyzes your paper, extracting key data, equations, figures, and methodologies with high precision.
                    </p>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Step 3 */}
            <motion.div
              className="relative z-10 mb-24 flex items-center"
              variants={{
                hidden: { opacity: 0, x: -50 },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: { duration: 0.5, delay: 0.6 }
                }
              }}
            >
              <div className="flex flex-col md:flex-row items-center">
                <motion.div
                  className="flex-shrink-0 bg-gradient-to-r from-primary to-highlight text-white text-2xl font-bold rounded-full h-20 w-20 flex items-center justify-center shadow-lg pulse-glow"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="relative z-10">3</span>
                  <div className="absolute inset-0 rounded-full bg-white opacity-20 blur-sm"></div>
                </motion.div>

                <motion.div
                  className="mt-4 md:mt-0 md:ml-8 glass-card p-6 rounded-xl shadow-lg max-w-2xl border border-white border-opacity-10"
                  whileHover={{
                    y: -5,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="absolute bottom-0 right-0 w-20 h-20 bg-primary opacity-5 rounded-full blur-xl"></div>
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-highlight">Generate Interactive Tools</h3>
                    <div className="mt-2 h-0.5 w-10 bg-gradient-to-r from-primary to-transparent rounded-full"></div>
                    <p className="mt-3 text-secondary">
                      Our platform transforms the extracted data into interactive tools, models, and APIs that you can use and share.
                    </p>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Step 4 */}
            <motion.div
              className="relative z-10 mb-16 flex items-center justify-end"
              variants={{
                hidden: { opacity: 0, x: 50 },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: { duration: 0.5, delay: 0.8 }
                }
              }}
            >
              <div className="flex flex-col md:flex-row-reverse items-center">
                <motion.div
                  className="flex-shrink-0 bg-gradient-to-r from-highlight to-accent text-white text-2xl font-bold rounded-full h-20 w-20 flex items-center justify-center shadow-lg pulse-glow"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="relative z-10">4</span>
                  <div className="absolute inset-0 rounded-full bg-white opacity-20 blur-sm"></div>
                </motion.div>

                <motion.div
                  className="mt-4 md:mt-0 md:mr-8 glass-card p-6 rounded-xl shadow-lg max-w-2xl border border-white border-opacity-10"
                  whileHover={{
                    y: -5,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="absolute bottom-0 left-0 w-20 h-20 bg-highlight opacity-5 rounded-full blur-xl"></div>
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-highlight to-accent">Integrate & Collaborate</h3>
                    <div className="mt-2 h-0.5 w-10 bg-gradient-to-r from-highlight to-transparent rounded-full"></div>
                    <p className="mt-3 text-secondary">
                      Use the generated tools in your workflow, share them with colleagues, or integrate them with other systems via our API.
                    </p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Testimonials Section */}
      <motion.div
        ref={testimonialsRef}
        initial="hidden"
        animate={testimonialsControls}
        variants={fadeInUp}
        className="py-20 relative overflow-hidden"
        id="testimonials"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-white"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-40 left-10 w-72 h-72 bg-accent opacity-5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.05, 0.08, 0.05]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: { duration: 0.5 }
              }
            }}
          >
            <motion.div
              className="inline-block"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-3xl font-extrabold text-accent sm:text-4xl">
                Trusted by <span className="text-transparent bg-clip-text bg-gradient-to-r from-highlight to-accent">Researchers</span> Worldwide
              </h2>
            </motion.div>
            <p className="mt-4 max-w-2xl text-xl text-secondary mx-auto font-light">
              See what scientists and research institutions are saying about SciML
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                quote: "SciML has revolutionized how we share our research findings. What used to take weeks now takes minutes.",
                author: "Dr. Sarah Chen",
                title: "Computational Biologist, Stanford University",
                delay: 0.2,
                gradient: "from-highlight to-accent"
              },
              {
                quote: "The ability to automatically extract models from papers and turn them into APIs has accelerated our research collaboration tenfold.",
                author: "Prof. Michael Rodriguez",
                title: "AI Research Lead, MIT",
                delay: 0.4,
                gradient: "from-accent to-primary"
              },
              {
                quote: "As a journal editor, I've seen SciML transform how researchers validate and build upon each other's work. It's the future of scientific publishing.",
                author: "Dr. Emily Watson",
                title: "Editor-in-Chief, Journal of Computational Science",
                delay: 0.6,
                gradient: "from-primary to-highlight"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                className="testimonial-card glass-card rounded-xl p-8 shadow-lg border border-white border-opacity-10 relative overflow-hidden"
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.5, delay: testimonial.delay }
                  }
                }}
                whileHover={{
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Card background gradient */}
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${testimonial.gradient}`}></div>
                <div className={`absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-r ${testimonial.gradient} rounded-full opacity-5 blur-xl`}></div>

                <div className="relative z-10">
                  <motion.div
                    className="flex items-center mb-6"
                    initial={{ opacity: 0.8 }}
                    whileHover={{
                      scale: 1.1,
                      opacity: 1
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className={`p-2 rounded-full bg-gradient-to-r ${testimonial.gradient}`}>
                      <svg className="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                      </svg>
                    </div>
                  </motion.div>

                  <p className="text-lg text-secondary mb-6 italic leading-relaxed">"{testimonial.quote}"</p>

                  <div className="flex items-center">
                    <div className="mr-4">
                      <div className={`h-12 w-12 rounded-full bg-gradient-to-r ${testimonial.gradient} flex items-center justify-center text-white font-bold text-lg`}>
                        {testimonial.author.charAt(0)}
                      </div>
                    </div>
                    <div>
                      <p className={`font-semibold text-transparent bg-clip-text bg-gradient-to-r ${testimonial.gradient}`}>
                        {testimonial.author}
                      </p>
                      <p className="text-sm text-secondary">{testimonial.title}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        ref={ctaRef}
        initial="hidden"
        animate={ctaControls}
        variants={fadeInUp}
        className="py-20 relative overflow-hidden"
        id="cta"
      >
        {/* Background elements */}
        <div className="absolute inset-0 animated-gradient"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden">
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              className="absolute rounded-full bg-white"
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                width: `${particle.size}px`,
                height: `${particle.size}px`,
                opacity: 0.2
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0.1, 0.3, 0.1]
              }}
              transition={{
                duration: particle.duration,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        {/* Decorative elements */}
        <motion.div
          className="absolute top-20 right-10 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.15, 0.1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>

        <motion.div
          className="absolute bottom-20 left-10 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.15, 0.1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        ></motion.div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-white">
          <div className="text-center">
            <motion.div
              className="inline-block"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.5 }
                }
              }}
            >
              <motion.h2
                className="text-4xl font-extrabold sm:text-5xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                Ready to transform your research?
              </motion.h2>
            </motion.div>

            <motion.div
              className="mt-6 max-w-2xl mx-auto"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.5, delay: 0.1 }
                }
              }}
            >
              <p className="text-xl opacity-90 leading-relaxed">
                Join thousands of researchers using SciML to make their work more accessible and impactful.
              </p>
            </motion.div>

            <motion.div
              className="mt-10 flex flex-wrap justify-center gap-6"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.5, delay: 0.2 }
                }
              }}
            >
              <motion.a
                href="#upload-paper"
                className="btn-futuristic px-8 py-4 bg-white text-highlight rounded-xl text-base font-medium transition-all duration-300 shadow-lg"
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 0 25px rgba(255, 255, 255, 0.5)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center">
                  <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Get Started for Free
                </span>
              </motion.a>

              <motion.a
                href="#learn-more"
                className="btn-futuristic px-8 py-4 bg-transparent border border-white text-white rounded-xl text-base font-medium transition-all duration-300"
                whileHover={{
                  scale: 1.05,
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  boxShadow: "0 0 15px rgba(255, 255, 255, 0.3)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center">
                  <svg className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Schedule a Demo
                </span>
              </motion.a>
            </motion.div>
          </div>
        </div>
      </motion.div>

      <Footer />
    </div>
  );
};

export default Home;
