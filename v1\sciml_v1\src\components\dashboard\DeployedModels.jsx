import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const DeployedModels = () => {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('date');

  // Mock data for deployed models
  useEffect(() => {
    const mockDeployedModels = [
      {
        id: 1,
        name: 'Climate_CNN_v1',
        type: 'CNN',
        status: 'deployed',
        accuracy: 94.2,
        paperId: 1,
        paperTitle: 'Deep Learning Approaches for Climate Modeling',
        createdDate: '2024-01-15T14:30:00Z',
        deployedDate: '2024-01-15T16:45:00Z',
        version: '1.0.0',
        framework: 'TensorFlow',
        size: '45.2 MB',
        predictions: 1247,
        apiCalls: 892,
        tags: ['Climate', 'CNN', 'Temperature'],
        description: 'Convolutional neural network for climate pattern prediction',
        deployment: {
          endpoint: 'https://api.sciml.com/v1/models/climate-cnn-v1',
          instances: 3,
          avgResponseTime: '120ms',
          uptime: '99.8%',
          region: 'us-east-1',
          scalingPolicy: 'auto',
          minInstances: 1,
          maxInstances: 10,
          cpuUtilization: 45,
          memoryUtilization: 62,
          requestsPerMinute: 23,
          errorRate: 0.2,
          lastHealthCheck: '2024-01-16T10:30:00Z',
          healthStatus: 'healthy'
        },
        apis: [
          { name: 'climate-prediction-api', requests: 567, status: 'active' },
          { name: 'weather-forecast-api', requests: 325, status: 'active' }
        ]
      },
      {
        id: 3,
        name: 'MedImage_ResNet',
        type: 'ResNet',
        status: 'deployed',
        accuracy: 96.7,
        paperId: 5,
        paperTitle: 'Computer Vision for Medical Image Analysis',
        createdDate: '2024-01-11T18:20:00Z',
        deployedDate: '2024-01-12T09:30:00Z',
        version: '1.2.1',
        framework: 'TensorFlow',
        size: '89.4 MB',
        predictions: 3456,
        apiCalls: 2341,
        tags: ['Medical', 'ResNet', 'Computer Vision'],
        description: 'ResNet architecture for medical image classification',
        deployment: {
          endpoint: 'https://api.sciml.com/v1/models/medimage-resnet',
          instances: 5,
          avgResponseTime: '95ms',
          uptime: '99.9%',
          region: 'us-west-2',
          scalingPolicy: 'manual',
          minInstances: 2,
          maxInstances: 8,
          cpuUtilization: 67,
          memoryUtilization: 78,
          requestsPerMinute: 45,
          errorRate: 0.1,
          lastHealthCheck: '2024-01-16T10:28:00Z',
          healthStatus: 'healthy'
        },
        apis: [
          { name: 'medical-image-api', requests: 2341, status: 'active' }
        ]
      },
      {
        id: 8,
        name: 'FinancePredict_LSTM',
        type: 'LSTM',
        status: 'deployed',
        accuracy: 91.3,
        paperId: 9,
        paperTitle: 'Time Series Analysis for Financial Markets',
        createdDate: '2024-01-10T12:15:00Z',
        deployedDate: '2024-01-11T08:45:00Z',
        version: '2.0.3',
        framework: 'PyTorch',
        size: '156.7 MB',
        predictions: 8923,
        apiCalls: 5678,
        tags: ['Finance', 'LSTM', 'Time Series'],
        description: 'LSTM network for financial market prediction and analysis',
        deployment: {
          endpoint: 'https://api.sciml.com/v1/models/finance-predict-lstm',
          instances: 4,
          avgResponseTime: '180ms',
          uptime: '99.5%',
          region: 'eu-west-1',
          scalingPolicy: 'auto',
          minInstances: 2,
          maxInstances: 12,
          cpuUtilization: 72,
          memoryUtilization: 85,
          requestsPerMinute: 67,
          errorRate: 0.3,
          lastHealthCheck: '2024-01-16T10:25:00Z',
          healthStatus: 'warning'
        },
        apis: [
          { name: 'stock-prediction-api', requests: 3456, status: 'active' },
          { name: 'market-analysis-api', requests: 2222, status: 'active' }
        ]
      }
    ];

    setTimeout(() => {
      setModels(mockDeployedModels);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort models
  const filteredModels = models
    .filter(model => {
      const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           model.paperTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           model.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.deployedDate) - new Date(a.deployedDate);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'accuracy':
          return b.accuracy - a.accuracy;
        case 'predictions':
          return b.predictions - a.predictions;
        case 'uptime':
          return parseFloat(b.deployment.uptime) - parseFloat(a.deployment.uptime);
        default:
          return 0;
      }
    });

  const getHealthStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'critical':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Deployed Models</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Monitor and manage your production AI models
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/models"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
                All Models
              </Link>
              <Link
                to="/dashboard/models/create"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 12l2 2 4-4" />
                </svg>
                Deploy Model
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Deployed Models',
              value: models.length,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              )
            },
            {
              label: 'Total Instances',
              value: models.reduce((sum, model) => sum + model.deployment.instances, 0),
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              )
            },
            {
              label: 'Total Predictions',
              value: models.reduce((sum, model) => sum + model.predictions, 0).toLocaleString(),
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              )
            },
            {
              label: 'Avg Uptime',
              value: models.length > 0 ? (models.reduce((sum, model) => sum + parseFloat(model.deployment.uptime), 0) / models.length).toFixed(1) + '%' : '0%',
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search deployed models..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Sort */}
            <div className="flex space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Deploy Date</option>
                <option value="name">Sort by Name</option>
                <option value="accuracy">Sort by Accuracy</option>
                <option value="predictions">Sort by Predictions</option>
                <option value="uptime">Sort by Uptime</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Deployed Models List */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {loading ? (
            <div className="bg-white rounded-xl p-8 text-center shadow-lg border border-gray-100">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading deployed models...</p>
            </div>
          ) : filteredModels.length === 0 ? (
            <div className="bg-white rounded-xl p-8 text-center shadow-lg border border-gray-100">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No deployed models found</h3>
              <p className="mt-2 text-primary">
                {searchQuery
                  ? 'Try adjusting your search criteria'
                  : 'Deploy your first model to see it here'
                }
              </p>
              {!searchQuery && (
                <Link
                  to="/dashboard/models/create"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Deploy Model
                </Link>
              )}
            </div>
          ) : (
            <AnimatePresence>
              {filteredModels.map((model, index) => (
                <motion.div
                  key={model.id}
                  className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-6">
                    {/* Model Header */}
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-semibold text-accent">
                            <Link to={`/dashboard/models/${model.id}`} className="hover:text-highlight transition-colors duration-200">
                              {model.name}
                            </Link>
                          </h3>
                          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            {model.type}
                          </span>
                          <span className="text-sm text-primary">v{model.version}</span>
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getHealthStatusColor(model.deployment.healthStatus)}`}>
                            {getHealthStatusIcon(model.deployment.healthStatus)}
                            <span className="ml-1 capitalize">{model.deployment.healthStatus}</span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{model.description}</p>
                        <p className="text-sm text-primary">
                          From: <Link to={`/dashboard/papers/${model.paperId}`} className="text-highlight hover:text-accent">
                            {model.paperTitle}
                          </Link>
                        </p>
                        <div className="flex flex-wrap gap-2 mt-3">
                          {model.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                        <div className="text-sm text-primary">
                          Deployed: {formatTimeAgo(model.deployedDate)}
                        </div>
                        <div className="text-sm text-primary">
                          Region: {model.deployment.region}
                        </div>
                        <div className="text-sm font-medium text-highlight">
                          {model.accuracy}% Accuracy
                        </div>
                      </div>
                    </div>

                    {/* Deployment Metrics */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="bg-green-50 rounded-lg p-3">
                        <div className="text-xs text-green-600">Uptime</div>
                        <div className="text-lg font-semibold text-green-800">{model.deployment.uptime}</div>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-3">
                        <div className="text-xs text-blue-600">Instances</div>
                        <div className="text-lg font-semibold text-blue-800">{model.deployment.instances}</div>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-3">
                        <div className="text-xs text-purple-600">Response Time</div>
                        <div className="text-lg font-semibold text-purple-800">{model.deployment.avgResponseTime}</div>
                      </div>
                      <div className="bg-yellow-50 rounded-lg p-3">
                        <div className="text-xs text-yellow-600">Requests/min</div>
                        <div className="text-lg font-semibold text-yellow-800">{model.deployment.requestsPerMinute}</div>
                      </div>
                    </div>

                    {/* Resource Usage */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-accent">CPU Utilization</span>
                          <span className="text-sm text-primary">{model.deployment.cpuUtilization}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${model.deployment.cpuUtilization}%` }}
                          />
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-accent">Memory Utilization</span>
                          <span className="text-sm text-primary">{model.deployment.memoryUtilization}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${model.deployment.memoryUtilization}%` }}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                      <Link
                        to={`/dashboard/models/${model.id}`}
                        className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200"
                      >
                        View Details
                      </Link>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200">
                        Monitor
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200">
                        Scale
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200">
                        API Docs
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-yellow-600 hover:text-yellow-800 transition-colors duration-200">
                        Maintenance
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                        Undeploy
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default DeployedModels;