type,title,author,page_count,word_count,has_images,has_tables,has_equations,content,score,category,description
document,,<PERSON><PERSON><PERSON><PERSON>,0.0,262.0,False,False,False,,,,
keyword,,,,,,,,tools,0.10079188640720667,tfidf,
keyword,,,,,,,,extraction,0.056357950080456846,tfidf,
keyword,,,,,,,,data,0.05555555555555555,tfidf,
keyword,,,,,,,,based,0.049360567579733464,tfidf,
keyword,,,,,,,,platform,0.047419632810737425,tfidf,
keyword,,,,,,,,model,0.04448705478295731,tfidf,
keyword,,,,,,,,models,0.04409646148508646,tfidf,
keyword,,,,,,,,key,0.040923918065780863,tfidf,
keyword,,,,,,,,upload,0.039283710065919304,tfidf,
keyword,,,,,,,,js,0.03770025733287012,tfidf,
keyword,,,,,,,,agent,0.037378276406457196,tfidf,
keyword,,,,,,,,ml,0.0372554075777688,tfidf,
keyword,,,,,,,,images,0.03649946651038004,tfidf,
keyword,,,,,,,,text,0.03649946651038004,tfidf,
keyword,,,,,,,,apis,0.030260706918919966,tfidf,
keyword,,,,,,,,datasets,0.030260706918919966,tfidf,
keyword,,,,,,,,layer,0.030260706918919966,tfidf,
keyword,,,,,,,,extracted,0.029242353661639072,tfidf,
keyword,,,,,,,,insights,0.029242353661639072,tfidf,
keyword,,,,,,,,metadata,0.028460107352488433,tfidf,
entity,,,,,,,,1,,CARDINAL,Numerals that do not fall under another type
entity,,,,,,,,PDF,,ORG,"Companies, agencies, institutions, etc."
entity,,,,,,,,metadata,,PERSON,"People, including fictional"
entity,,,,,,,,Tools,,PERSON,"People, including fictional"
entity,,,,,,,,PyMuPDF,,GPE,"Countries, cities, states"
entity,,,,,,,,Grobid,,PERSON,"People, including fictional"
entity,,,,,,,,2,,CARDINAL,Numerals that do not fall under another type
entity,,,,,,,,"NLP & Insight Extraction 
• 
Text Segmentation: Title",,ORG,"Companies, agencies, institutions, etc."
entity,,,,,,,,KeyBERT,,PERSON,"People, including fictional"
entity,,,,,,,,3,,CARDINAL,Numerals that do not fall under another type
entity,,,,,,,,Tools,,PERSON,"People, including fictional"
entity,,,,,,,,TableNet,,ORG,"Companies, agencies, institutions, etc."
entity,,,,,,,,ML,,ORG,"Companies, agencies, institutions, etc."
entity,,,,,,,,Tools,,PERSON,"People, including fictional"
entity,,,,,,,,4,,CARDINAL,Numerals that do not fall under another type
entity,,,,,,,,"• 
Handle",,PRODUCT,"Objects, vehicles, foods, etc. (not services)"
entity,,,,,,,,5,,CARDINAL,Numerals that do not fall under another type
entity,,,,,,,,ML,,ORG,"Companies, agencies, institutions, etc."
entity,,,,,,,,"• 
Train",,PRODUCT,"Objects, vehicles, foods, etc. (not services)"
entity,,,,,,,,PyTorch,,PERSON,"People, including fictional"
