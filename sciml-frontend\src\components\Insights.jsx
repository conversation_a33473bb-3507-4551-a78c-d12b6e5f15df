import { useState } from 'react';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/Insights.css';

const Insights = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Insights' },
    { id: 'research', name: 'Research Papers' },
    { id: 'case-studies', name: 'Case Studies' },
    { id: 'tutorials', name: '<PERSON><PERSON><PERSON>' },
    { id: 'news', name: 'News & Updates' }
  ];

  // Sample insights data
  const insightsData = [
    {
      id: 1,
      title: 'Transforming Research Papers into Interactive Tools',
      category: 'research',
      date: 'June 15, 2023',
      author: 'Dr. <PERSON>',
      image: 'https://via.placeholder.com/600x400?text=Research+Paper',
      excerpt: 'Learn how SciML is revolutionizing the way researchers share and interact with scientific findings.',
      tags: ['AI', 'Machine Learning', 'Research']
    },
    {
      id: 2,
      title: 'Case Study: How Stanford University Uses SciML',
      category: 'case-studies',
      date: 'May 22, 2023',
      author: 'Prof. Michael Rodriguez',
      image: 'https://via.placeholder.com/600x400?text=Case+Study',
      excerpt: 'Stanford University researchers have increased collaboration efficiency by 40% using SciML tools.',
      tags: ['Case Study', 'University', 'Collaboration']
    },
    {
      id: 3,
      title: 'Getting Started with SciML: A Beginner\'s Guide',
      category: 'tutorials',
      date: 'April 10, 2023',
      author: 'Emily Watson',
      image: 'https://via.placeholder.com/600x400?text=Tutorial',
      excerpt: 'This step-by-step tutorial will help you get started with SciML and make the most of its features.',
      tags: ['Tutorial', 'Beginner', 'Guide']
    },
    {
      id: 4,
      title: 'SciML Raises $10M in Series A Funding',
      category: 'news',
      date: 'March 5, 2023',
      author: 'SciML Team',
      image: 'https://via.placeholder.com/600x400?text=News',
      excerpt: 'SciML has secured $10 million in Series A funding to expand its platform and reach more researchers.',
      tags: ['News', 'Funding', 'Growth']
    },
    {
      id: 5,
      title: 'The Future of Scientific Publishing with AI',
      category: 'research',
      date: 'February 18, 2023',
      author: 'Dr. James Wilson',
      image: 'https://via.placeholder.com/600x400?text=Research+Paper',
      excerpt: 'Exploring how AI and machine learning are transforming the landscape of scientific publishing.',
      tags: ['AI', 'Publishing', 'Future']
    },
    {
      id: 6,
      title: 'How to Extract Data from Research Papers',
      category: 'tutorials',
      date: 'January 30, 2023',
      author: 'Alex Johnson',
      image: 'https://via.placeholder.com/600x400?text=Tutorial',
      excerpt: 'Learn how to use SciML\'s data extraction tools to pull valuable information from research papers.',
      tags: ['Data Extraction', 'Tutorial', 'Tools']
    }
  ];

  // Filter insights based on active category and search query
  const filteredInsights = insightsData.filter(insight => {
    const matchesCategory = activeCategory === 'all' || insight.category === activeCategory;
    const matchesSearch = searchQuery === '' ||
      insight.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      insight.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      insight.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow pt-24 lg:pt-20 pb-8 lg:pb-12 px-4 bg-white">
        {/* Decorative elements */}
        <div className="absolute top-1/4 left-1/3 w-64 h-64 lg:w-96 lg:h-96 bg-highlight opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-primary opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-8 lg:mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl sm:text-4xl font-bold text-accent">
              SciML <span className="text-highlight">Insights</span>
            </h1>
            <p className="mt-3 lg:mt-4 text-base lg:text-lg text-primary max-w-2xl mx-auto">
              Explore the latest research, case studies, tutorials, and news from the world of scientific machine learning.
            </p>
          </motion.div>

          {/* Search and filter */}
          <motion.div
            className="mb-8 lg:mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
              {/* Categories */}
              <div className="flex flex-wrap gap-2 justify-center lg:justify-start">
                {categories.map(category => (
                  <motion.button
                    key={category.id}
                    className={`px-3 lg:px-4 py-2 rounded-full text-xs lg:text-sm font-medium transition-all duration-300 ${
                      activeCategory === category.id
                        ? 'bg-highlight text-white shadow-lg'
                        : 'bg-gray-100 text-accent hover:bg-gray-200'
                    }`}
                    onClick={() => setActiveCategory(category.id)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {category.name}
                  </motion.button>
                ))}
              </div>

              {/* Search */}
              <div className="relative w-full lg:w-64">
                <input
                  type="text"
                  className="w-full px-3 lg:px-4 py-2 pl-8 lg:pl-10 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight focus:border-transparent text-primary text-sm lg:text-base"
                  placeholder="Search insights..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                <svg
                  className="absolute left-2 lg:left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </motion.div>

          {/* Insights grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {filteredInsights.length > 0 ? (
              filteredInsights.map(insight => (
                <motion.div
                  key={insight.id}
                  className="bg-white rounded-xl overflow-hidden shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300"
                  variants={itemVariants}
                  whileHover={{ y: -10 }}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={insight.image}
                      alt={insight.title}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                    />
                    <div className="absolute top-0 right-0 m-4">
                      <span className="px-3 py-1 text-xs font-medium bg-highlight text-white rounded-full">
                        {categories.find(cat => cat.id === insight.category)?.name || insight.category}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center text-xs text-primary mb-2">
                      <span>{insight.date}</span>
                      <span className="mx-2">•</span>
                      <span>{insight.author}</span>
                    </div>

                    <h3 className="text-xl font-semibold text-accent mb-2 hover:text-highlight transition-colors duration-200">
                      {insight.title}
                    </h3>

                    <p className="text-primary mb-4">
                      {insight.excerpt}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {insight.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 text-xs bg-gray-100 text-accent rounded"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>

                    <motion.a
                      href={`#insight/${insight.id}`}
                      className="inline-flex items-center text-highlight hover:text-accent transition-colors duration-200"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      Read more
                      <svg
                        className="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </motion.a>
                  </div>
                </motion.div>
              ))
            ) : (
              <motion.div
                className="col-span-full text-center py-12"
                variants={itemVariants}
              >
                <svg
                  className="mx-auto h-12 w-12 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h3 className="mt-4 text-xl font-medium text-accent">No results found</h3>
                <p className="mt-2 text-primary">
                  Try adjusting your search or filter criteria
                </p>
              </motion.div>
            )}
          </motion.div>

          {/* Pagination */}
          {filteredInsights.length > 0 && (
            <motion.div
              className="mt-12 flex justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <nav className="flex items-center space-x-2">
                <button className="px-3 py-2 rounded-md bg-gray-100 text-accent hover:bg-gray-200 transition-colors duration-200">
                  Previous
                </button>
                {[1, 2, 3].map(page => (
                  <button
                    key={page}
                    className={`w-10 h-10 rounded-md flex items-center justify-center ${
                      page === 1
                        ? 'bg-highlight text-white'
                        : 'bg-gray-100 text-accent hover:bg-gray-200'
                    } transition-colors duration-200`}
                  >
                    {page}
                  </button>
                ))}
                <button className="px-3 py-2 rounded-md bg-gray-100 text-accent hover:bg-gray-200 transition-colors duration-200">
                  Next
                </button>
              </nav>
            </motion.div>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Insights;
