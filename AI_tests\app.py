#!/usr/bin/env python3
"""
SciML Platform - Web Application
A beautiful web interface for scientific literature analysis
"""

import os
import json
import uuid
import re
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename
import tempfile
import threading
from pathlib import Path
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import base64
from io import BytesIO
from groq import Groq

# Import our SciML platform
from part_1 import SciMLPlatform

app = Flask(__name__)
app.config['SECRET_KEY'] = 'sciml-platform-2024'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# Create persistent storage directories
STORAGE_DIR = Path('storage')
STATUS_DIR = STORAGE_DIR / 'status'
RESULTS_DIR = STORAGE_DIR / 'results'

# Create directories if they don't exist
STORAGE_DIR.mkdir(exist_ok=True)
STATUS_DIR.mkdir(exist_ok=True)
RESULTS_DIR.mkdir(exist_ok=True)

# Initialize SciML Platform
platform = SciMLPlatform()

# Initialize Groq client
GROQ_API_KEY = "********************************************************"
groq_client = Groq(api_key=GROQ_API_KEY)

# Timeout wrapper for processing
class TimeoutError(Exception):
    pass

def timeout_handler(func, args, timeout_duration=300):  # 5 minutes timeout
    """Execute function with timeout"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func(*args)
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_duration)

    if thread.is_alive():
        # Thread is still running, timeout occurred
        raise TimeoutError(f"Processing timed out after {timeout_duration} seconds")

    if exception[0]:
        raise exception[0]

    return result[0]

# Persistent storage functions
def save_status(task_id, status_data):
    """Save processing status to disk"""
    status_file = STATUS_DIR / f"{task_id}.json"
    with open(status_file, 'w') as f:
        json.dump(status_data, f)

def load_status(task_id):
    """Load processing status from disk"""
    status_file = STATUS_DIR / f"{task_id}.json"
    if status_file.exists():
        with open(status_file, 'r') as f:
            return json.load(f)
    return None

def save_results(task_id, results_data):
    """Save processing results to disk"""
    results_file = RESULTS_DIR / f"{task_id}.json"
    with open(results_file, 'w') as f:
        json.dump(results_data, f, default=str)

def load_results(task_id):
    """Load processing results from disk"""
    results_file = RESULTS_DIR / f"{task_id}.json"
    if results_file.exists():
        with open(results_file, 'r') as f:
            return json.load(f)
    return None

def update_status(task_id, **kwargs):
    """Update specific fields in status"""
    status = load_status(task_id) or {}
    status.update(kwargs)
    save_status(task_id, status)
    return status

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), f"{task_id}_{filename}")
        file.save(temp_path)

        # Start processing in background
        save_status(task_id, {
            'status': 'processing',
            'message': 'Analyzing document...',
            'progress': 0,
            'filename': filename,
            'started_at': datetime.now().isoformat()
        })

        # Process in background thread
        thread = threading.Thread(target=process_document_async, args=(task_id, temp_path, False))
        thread.daemon = True
        thread.start()

        return jsonify({'task_id': task_id, 'status': 'started'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze-url', methods=['POST'])
def analyze_url():
    """Handle URL analysis"""
    try:
        data = request.get_json()
        if not data or 'url' not in data:
            return jsonify({'error': 'No URL provided'}), 400

        url = data['url'].strip()
        if not url:
            return jsonify({'error': 'Empty URL provided'}), 400

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Start processing in background
        save_status(task_id, {
            'status': 'processing',
            'message': 'Downloading and analyzing document...',
            'progress': 0,
            'url': url,
            'started_at': datetime.now().isoformat()
        })

        # Process in background thread
        thread = threading.Thread(target=process_document_async, args=(task_id, url, True))
        thread.daemon = True
        thread.start()

        return jsonify({'task_id': task_id, 'status': 'started'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """Get processing status"""
    status = load_status(task_id)
    if not status:
        return jsonify({'error': 'Task not found'}), 404

    # If completed, include results
    if status['status'] == 'completed':
        results = load_results(task_id)
        if results:
            status['results'] = results

    return jsonify(status)

@app.route('/api/results/<task_id>')
def get_results(task_id):
    """Get full results"""
    results = load_results(task_id)
    if not results:
        return jsonify({'error': 'Results not found'}), 404

    return jsonify(results)

@app.route('/api/visualizations/<task_id>')
def get_visualizations(task_id):
    """Generate and return visualizations for the analysis"""
    try:
        results = load_results(task_id)
        if not results:
            return jsonify({'error': 'Results not found'}), 404

        visualizations = generate_visualizations(results)
        return jsonify(visualizations)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/datasets/<task_id>')
def extract_datasets(task_id):
    """Extract dataset links from the paper"""
    try:
        results = load_results(task_id)
        if not results:
            return jsonify({'error': 'Results not found'}), 404

        datasets = extract_dataset_links(results)
        return jsonify(datasets)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-models/<task_id>')
def generate_models(task_id):
    """Generate model code using Groq AI"""
    try:
        results = load_results(task_id)
        if not results:
            return jsonify({'error': 'Results not found'}), 404

        models = generate_model_code(results)
        return jsonify(models)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def process_document_async(task_id, input_path, is_url):
    """Process document in background thread"""
    try:
        # Update status
        update_status(task_id, progress=10, message='Initializing analysis...')

        # Add timeout for URL processing
        if is_url:
            update_status(task_id, progress=20, message='Downloading document...')

        # Process document with timeout handling
        update_status(task_id, progress=30, message='Extracting content...')

        # Process document with timeout and OCR handling
        try:
            if is_url:
                # Use timeout for URL processing and disable OCR to prevent hanging
                import part_1
                original_ocr = part_1.OCR_AVAILABLE
                part_1.OCR_AVAILABLE = False

                try:
                    # Use timeout wrapper for URL processing
                    result = timeout_handler(platform.process_document, (input_path, is_url), 300)
                finally:
                    # Restore OCR setting
                    part_1.OCR_AVAILABLE = original_ocr
            else:
                # Local file processing with shorter timeout
                result = timeout_handler(platform.process_document, (input_path, is_url), 600)

        except TimeoutError as e:
            update_status(task_id, status='error', message=str(e))
            return
        except Exception as e:
            update_status(task_id, status='error', message=f'Processing error: {str(e)}')
            return

        if 'error' in result:
            update_status(task_id, status='error', message=result['error'])
            return

        # Update progress
        update_status(task_id, progress=80, message='Finalizing analysis...')

        # Store results
        save_results(task_id, result)

        # Mark as completed
        update_status(task_id,
                     status='completed',
                     progress=100,
                     message='Analysis completed successfully!',
                     completed_at=datetime.now().isoformat())

        # Clean up temporary file if it was uploaded
        if not is_url and os.path.exists(input_path):
            try:
                os.remove(input_path)
            except:
                pass

    except Exception as e:
        error_msg = f'Error: {str(e)}'
        print(f"Processing error for task {task_id}: {error_msg}")  # Debug log
        update_status(task_id, status='error', message=error_msg)

        # Clean up temporary file on error
        if not is_url and os.path.exists(input_path):
            try:
                os.remove(input_path)
            except:
                pass

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/debug/tasks')
def debug_tasks():
    """Debug endpoint to list all tasks"""
    tasks = []
    if STATUS_DIR.exists():
        for status_file in STATUS_DIR.glob('*.json'):
            try:
                with open(status_file, 'r') as f:
                    status_data = json.load(f)
                    tasks.append({
                        'task_id': status_file.stem,
                        'status': status_data.get('status', 'unknown'),
                        'message': status_data.get('message', ''),
                        'progress': status_data.get('progress', 0),
                        'started_at': status_data.get('started_at', '')
                    })
            except Exception as e:
                tasks.append({
                    'task_id': status_file.stem,
                    'status': 'error',
                    'message': f'Failed to read status: {e}'
                })

    return jsonify({
        'total_tasks': len(tasks),
        'tasks': tasks
    })

def generate_visualizations(results):
    """Generate visualization graphs from analysis results"""
    visualizations = {}

    try:
        # Keywords frequency chart
        if 'insights' in results and 'tfidf_keywords' in results['insights']:
            keywords_chart = create_keywords_chart(results['insights']['tfidf_keywords'])
            visualizations['keywords_chart'] = keywords_chart

        # Entities distribution chart
        if 'insights' in results and 'named_entities' in results['insights']:
            entities_chart = create_entities_chart(results['insights']['named_entities'])
            visualizations['entities_chart'] = entities_chart

        # Document statistics chart
        if 'metadata' in results:
            stats_chart = create_stats_chart(results)
            visualizations['stats_chart'] = stats_chart

        # Content distribution chart
        content_chart = create_content_distribution_chart(results)
        visualizations['content_chart'] = content_chart

    except Exception as e:
        print(f"Error generating visualizations: {e}")
        visualizations['error'] = str(e)

    return visualizations

def create_keywords_chart(keywords):
    """Create keywords frequency chart"""
    try:
        # Take top 15 keywords
        top_keywords = keywords[:15]
        words = [kw[0] for kw in top_keywords]
        scores = [kw[1] for kw in top_keywords]

        plt.figure(figsize=(12, 6))
        bars = plt.bar(range(len(words)), scores, color='#00b5ad', alpha=0.8)
        plt.xlabel('Keywords', fontsize=12)
        plt.ylabel('TF-IDF Score', fontsize=12)
        plt.title('Top Keywords by TF-IDF Score', fontsize=14, fontweight='bold')
        plt.xticks(range(len(words)), words, rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()

        # Convert to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            'type': 'keywords',
            'title': 'Keywords Frequency Analysis',
            'data': chart_data,
            'description': f'Top {len(words)} keywords extracted using TF-IDF analysis'
        }
    except Exception as e:
        return {'error': str(e)}

def create_entities_chart(entities):
    """Create named entities distribution chart"""
    try:
        # Count entities by type
        entity_counts = {}
        for entity in entities:
            label = entity.get('label', 'OTHER')
            entity_counts[label] = entity_counts.get(label, 0) + 1

        if not entity_counts:
            return {'error': 'No entities found'}

        labels = list(entity_counts.keys())
        counts = list(entity_counts.values())

        plt.figure(figsize=(10, 8))
        colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
        wedges, texts, autotexts = plt.pie(counts, labels=labels, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        plt.title('Named Entities Distribution', fontsize=14, fontweight='bold')

        # Convert to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            'type': 'entities',
            'title': 'Named Entities Distribution',
            'data': chart_data,
            'description': f'Distribution of {sum(counts)} named entities across {len(labels)} categories'
        }
    except Exception as e:
        return {'error': str(e)}

def create_stats_chart(results):
    """Create document statistics chart"""
    try:
        stats = {
            'Pages': results.get('metadata', {}).get('pages', 0),
            'Images': len(results.get('images', [])),
            'Tables': len(results.get('tables', [])),
            'Keywords': len(results.get('insights', {}).get('tfidf_keywords', [])),
            'Entities': len(results.get('insights', {}).get('named_entities', [])),
            'Equations': len(results.get('equations', []))
        }

        labels = list(stats.keys())
        values = list(stats.values())

        plt.figure(figsize=(10, 6))
        bars = plt.bar(labels, values, color=['#003f5c', '#2f4b7c', '#00b5ad', '#4a6fa5', '#7a9cc6', '#a8c8ec'])
        plt.xlabel('Document Elements', fontsize=12)
        plt.ylabel('Count', fontsize=12)
        plt.title('Document Statistics Overview', fontsize=14, fontweight='bold')
        plt.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(value), ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # Convert to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            'type': 'stats',
            'title': 'Document Statistics',
            'data': chart_data,
            'description': 'Overview of document content and extracted elements'
        }
    except Exception as e:
        return {'error': str(e)}

def create_content_distribution_chart(results):
    """Create content distribution chart"""
    try:
        text_length = len(results.get('text', ''))
        images_count = len(results.get('images', []))
        tables_count = len(results.get('tables', []))

        # Calculate content distribution
        content_types = ['Text Content', 'Visual Content', 'Structured Data']
        content_values = [
            text_length / 1000,  # Text in thousands of characters
            images_count * 10,   # Weight images
            tables_count * 15    # Weight tables more
        ]

        if sum(content_values) == 0:
            return {'error': 'No content to visualize'}

        plt.figure(figsize=(8, 8))
        colors = ['#003f5c', '#00b5ad', '#2f4b7c']
        plt.pie(content_values, labels=content_types, autopct='%1.1f%%',
                colors=colors, startangle=90)
        plt.title('Content Distribution Analysis', fontsize=14, fontweight='bold')

        # Convert to base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return {
            'type': 'content',
            'title': 'Content Distribution',
            'data': chart_data,
            'description': 'Distribution of different content types in the document'
        }
    except Exception as e:
        return {'error': str(e)}

def extract_dataset_links(results):
    """Extract dataset links and references from the paper"""
    datasets = {
        'found_links': [],
        'mentioned_datasets': [],
        'data_availability': None
    }

    try:
        text = results.get('text', '')

        # Common dataset URL patterns
        url_patterns = [
            r'https?://(?:www\.)?(?:kaggle\.com|github\.com|zenodo\.org|figshare\.com|dryad\.org)/[^\s\)]+',
            r'https?://(?:www\.)?(?:data\.gov|opendata|dataset)[^\s\)]*',
            r'https?://[^\s]*(?:dataset|data)[^\s\)]*',
            r'doi\.org/[^\s\)]+',
            r'https?://[^\s]*\.(?:csv|json|xlsx?|h5|hdf5|parquet)[^\s\)]*'
        ]

        # Extract URLs
        for pattern in url_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if match not in datasets['found_links']:
                    datasets['found_links'].append({
                        'url': match,
                        'type': 'direct_link',
                        'description': 'Dataset link found in paper'
                    })

        # Common dataset names and repositories
        dataset_keywords = [
            'ImageNet', 'CIFAR', 'MNIST', 'COCO', 'Pascal VOC', 'Open Images',
            'Common Crawl', 'Wikipedia', 'BookCorpus', 'SQuAD', 'GLUE',
            'UCI Machine Learning', 'Kaggle', 'GitHub', 'Zenodo',
            'Google Dataset Search', 'AWS Open Data', 'Microsoft Research'
        ]

        # Find mentioned datasets
        for keyword in dataset_keywords:
            if keyword.lower() in text.lower():
                datasets['mentioned_datasets'].append({
                    'name': keyword,
                    'context': 'Found in paper text',
                    'search_url': f"https://www.google.com/search?q={keyword.replace(' ', '+')}+dataset"
                })

        # Look for data availability statements
        availability_patterns = [
            r'data.*(?:available|accessible).*(?:at|from|via)[^\.\n]*',
            r'dataset.*(?:can be|is).*(?:found|downloaded|accessed)[^\.\n]*',
            r'code.*(?:available|accessible).*(?:at|from|via)[^\.\n]*'
        ]

        for pattern in availability_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                datasets['data_availability'] = matches[0][:200] + "..." if len(matches[0]) > 200 else matches[0]
                break

        # Add common dataset repositories as suggestions
        datasets['suggested_repositories'] = [
            {
                'name': 'Kaggle Datasets',
                'url': 'https://www.kaggle.com/datasets',
                'description': 'Large collection of datasets for machine learning'
            },
            {
                'name': 'Google Dataset Search',
                'url': 'https://datasetsearch.research.google.com/',
                'description': 'Search engine for datasets'
            },
            {
                'name': 'Papers With Code Datasets',
                'url': 'https://paperswithcode.com/datasets',
                'description': 'Datasets linked to research papers'
            },
            {
                'name': 'UCI ML Repository',
                'url': 'https://archive.ics.uci.edu/ml/index.php',
                'description': 'Classic machine learning datasets'
            },
            {
                'name': 'Zenodo',
                'url': 'https://zenodo.org/',
                'description': 'Research data repository'
            }
        ]

    except Exception as e:
        datasets['error'] = str(e)

    return datasets

def generate_model_code(results):
    """Generate model code using Groq AI based on paper analysis"""
    models = {
        'generated_models': [],
        'analysis_summary': '',
        'recommendations': []
    }

    try:
        # Prepare context from analysis results
        text = results.get('text', '')[:8000]  # Limit text for API
        keywords = results.get('insights', {}).get('tfidf_keywords', [])[:10]
        entities = results.get('insights', {}).get('named_entities', [])[:10]

        # Create analysis summary
        keyword_text = ', '.join([kw[0] for kw in keywords])
        entity_text = ', '.join([ent.get('text', '') for ent in entities])

        # Construct prompt for Groq
        prompt = f"""
        Analyze this research paper and generate Python model code based on the methodology described.

        Paper Content (excerpt): {text[:3000]}

        Key Topics: {keyword_text}
        Named Entities: {entity_text}

        Please:
        1. Identify the main machine learning/AI models or architectures mentioned
        2. Generate complete, runnable Python code for each model
        3. Include proper imports, data preprocessing, model definition, training, and evaluation
        4. Use popular frameworks like TensorFlow, PyTorch, or scikit-learn as appropriate
        5. Add detailed comments explaining each part
        6. If multiple models are mentioned, provide code for each

        Format your response as JSON with this structure:
        {{
            "models": [
                {{
                    "name": "Model Name",
                    "description": "Brief description",
                    "framework": "tensorflow/pytorch/sklearn",
                    "code": "Complete Python code",
                    "requirements": ["list", "of", "dependencies"]
                }}
            ],
            "summary": "Brief analysis of the paper's methodology"
        }}
        """

        # Call Groq API
        response = groq_client.chat.completions.create(
            model="llama-3.1-70b-versatile",
            messages=[
                {"role": "system", "content": "You are an expert AI researcher and Python developer. Generate high-quality, production-ready model code based on research papers."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=4000
        )

        # Parse response
        response_text = response.choices[0].message.content

        # Try to extract JSON from response
        try:
            # Look for JSON in the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_content = response_text[json_start:json_end]
                parsed_response = json.loads(json_content)
                models['generated_models'] = parsed_response.get('models', [])
                models['analysis_summary'] = parsed_response.get('summary', '')
            else:
                # Fallback: treat entire response as summary
                models['analysis_summary'] = response_text
        except json.JSONDecodeError:
            # If JSON parsing fails, create a simple model structure
            models['analysis_summary'] = response_text
            models['generated_models'] = [{
                'name': 'Generated Model',
                'description': 'Model code generated from paper analysis',
                'framework': 'tensorflow',
                'code': response_text,
                'requirements': ['tensorflow', 'numpy', 'pandas', 'scikit-learn']
            }]

        # Add recommendations
        models['recommendations'] = [
            "Review the generated code and adapt it to your specific dataset",
            "Ensure all required dependencies are installed",
            "Validate the model architecture matches your requirements",
            "Consider hyperparameter tuning for optimal performance",
            "Test the code with sample data before production use"
        ]

    except Exception as e:
        models['error'] = str(e)
        models['analysis_summary'] = f"Error generating models: {str(e)}"

    return models

if __name__ == '__main__':
    # Create templates and static directories
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    print("🚀 Starting SciML Platform Web Application...")
    print("📱 Access the application at: http://localhost:5000")
    print("🔧 Health check available at: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5000, debug=True)
