2025-05-29 22:08:32,820 - INFO - SciML Platform initialized
2025-05-29 22:08:32,822 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:08:32,823 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:08:32,924 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,924 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,927 - WARNING - <PERSON>ropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,928 - WARNING - <PERSON>ropB<PERSON> missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,929 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,931 - WARNING - <PERSON>rop<PERSON><PERSON> missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,933 - WARNING - <PERSON><PERSON><PERSON><PERSON> missing from /Page, defaulting to MediaBox
2025-05-29 22:08:32,933 - WARNING - <PERSON>rop<PERSON><PERSON> missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,708 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,709 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,709 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,710 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,710 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,710 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,711 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,711 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:08:35,716 - INFO - Segmenting text into sections...
2025-05-29 22:08:35,731 - INFO - Extracting key phrases and insights...
2025-05-29 22:08:35,738 - ERROR - TF-IDF extraction error: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\nltk_data'
    - 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\share\\nltk_data'
    - 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-29 22:08:39,330 - ERROR - Text statistics error: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\nltk_data'
    - 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\share\\nltk_data'
    - 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-29 22:08:39,340 - INFO - Analysis saved to: output\analysis_20250529_220839.json
2025-05-29 22:08:39,354 - ERROR - Unexpected error: Unknown format code 'f' for object of type 'str'
2025-05-29 22:26:56,343 - INFO - SciML Platform initialized
2025-05-29 22:26:56,344 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:26:56,344 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:26:57,491 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,491 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,492 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,492 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,492 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,493 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,493 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:57,493 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,266 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,266 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:26:58,267 - INFO - Segmenting text into sections...
2025-05-29 22:26:58,274 - INFO - Extracting key phrases and insights...
2025-05-29 22:27:00,963 - INFO - Building structured dataset...
2025-05-29 22:27:01,029 - INFO - Dataset exported to CSV: output\dataset_20250529_222700.csv
2025-05-29 22:27:01,030 - INFO - Analysis saved to: output\analysis_20250529_222700.json
2025-05-29 22:27:01,030 - INFO - Dataset exported to: output\dataset_20250529_222700.csv
2025-05-29 22:27:13,794 - INFO - SciML Platform initialized
2025-05-29 22:27:13,794 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:27:13,794 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:27:14,626 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,626 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,627 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,627 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,629 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,629 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,630 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:14,631 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,389 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,390 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,390 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,391 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,392 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,392 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,392 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,392 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:27:15,392 - INFO - Segmenting text into sections...
2025-05-29 22:27:15,400 - INFO - Extracting key phrases and insights...
2025-05-29 22:27:17,728 - INFO - Building structured dataset...
2025-05-29 22:27:17,782 - INFO - Dataset exported to CSV: output\dataset_20250529_222717.csv
2025-05-29 22:27:17,783 - INFO - Analysis saved to: output\analysis_20250529_222717.json
2025-05-29 22:27:17,783 - INFO - Dataset exported to: output\dataset_20250529_222717.csv
2025-05-29 22:30:52,718 - INFO - SciML Platform initialized
2025-05-29 22:30:52,719 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:30:52,719 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:30:53,548 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,549 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,549 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,550 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,550 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,551 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,551 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:53,552 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,432 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,432 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,433 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,433 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,433 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,434 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,434 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,434 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:30:54,434 - INFO - Segmenting text into sections...
2025-05-29 22:30:54,442 - INFO - Extracting key phrases and insights...
2025-05-29 22:30:56,746 - INFO - Building structured dataset...
2025-05-29 22:30:56,798 - INFO - Dataset exported to CSV: output\dataset_20250529_223056.csv
2025-05-29 22:30:56,798 - INFO - Analysis saved to: output\analysis_20250529_223056.json
2025-05-29 22:30:56,798 - INFO - Dataset exported to: output\dataset_20250529_223056.csv
2025-05-29 22:32:18,417 - INFO - SciML Platform initialized
2025-05-29 22:32:18,417 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 22:32:18,418 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 22:32:18,937 - INFO - Downloaded to: output\downloaded_20250529_223218.pdf
2025-05-29 22:32:18,938 - INFO - Parsing PDF: output\downloaded_20250529_223218.pdf
2025-05-29 22:32:19,057 - ERROR - Error parsing PDF: Failed to open file 'output\\downloaded_20250529_223218.pdf'.
2025-05-29 22:35:21,683 - INFO - SciML Platform initialized
2025-05-29 22:35:21,684 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 22:35:21,684 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 22:35:21,684 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 22:35:24,854 - INFO - Download progress: 36.8%
2025-05-29 22:35:27,933 - INFO - Download progress: 73.6%
2025-05-29 22:35:29,340 - INFO - Successfully downloaded PDF to: output\downloaded_20250529_223521.pdf (2,850,797 bytes)
2025-05-29 22:35:29,341 - INFO - Parsing PDF: output\downloaded_20250529_223521.pdf
2025-05-29 22:35:30,332 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,333 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,333 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,334 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,334 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,335 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,336 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:30,336 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,281 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,282 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,282 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,283 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,283 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,283 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,284 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,284 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:35:31,284 - INFO - Segmenting text into sections...
2025-05-29 22:35:31,294 - INFO - Extracting key phrases and insights...
2025-05-29 22:35:34,048 - INFO - Building structured dataset...
2025-05-29 22:35:34,110 - INFO - Dataset exported to CSV: output\dataset_20250529_223534.csv
2025-05-29 22:35:34,111 - INFO - Analysis saved to: output\analysis_20250529_223534.json
2025-05-29 22:35:34,111 - INFO - Dataset exported to: output\dataset_20250529_223534.csv
2025-05-29 22:36:48,750 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,751 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,751 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,752 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,753 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,753 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,754 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:48,754 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,519 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,520 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,520 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,520 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,521 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,521 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,522 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:36:49,522 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,084 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,107 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,108 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,108 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,109 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,110 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,111 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:53,111 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,013 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,014 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,015 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,016 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,016 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,017 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,017 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:46:57,018 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:47:22,424 - INFO - SciML Platform initialized
2025-05-29 22:47:22,425 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:47:22,425 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:51:28,963 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,970 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,971 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,973 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,975 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,976 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,977 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:28,978 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,577 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,578 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,578 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,578 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,578 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,579 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,579 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:31,579 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:51:51,990 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 22:53:25,967 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,969 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,970 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,971 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,972 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,973 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,980 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:25,981 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,411 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,411 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,412 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,412 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,413 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,414 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,414 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:28,416 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:53:46,533 - INFO - SciML Platform initialized
2025-05-29 22:53:46,534 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:53:46,534 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:53:46,815 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 22:55:16,546 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,546 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,547 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,547 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,547 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,548 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,548 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:16,549 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,357 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,358 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,358 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,358 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,359 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,359 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,359 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,359 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:55:17,360 - INFO - Segmenting text into sections...
2025-05-29 22:55:17,368 - INFO - Extracting key phrases and insights...
2025-05-29 22:55:19,574 - INFO - Building structured dataset...
2025-05-29 22:55:19,627 - INFO - Dataset exported to CSV: output\dataset_20250529_225519.csv
2025-05-29 22:55:19,628 - INFO - Analysis saved to: output\analysis_20250529_225519.json
2025-05-29 22:55:19,628 - INFO - Dataset exported to: output\dataset_20250529_225519.csv
2025-05-29 22:56:31,937 - INFO - SciML Platform initialized
2025-05-29 22:56:31,939 - INFO - Processing file: Gesture_Control_Drone.pdf
2025-05-29 22:56:31,939 - INFO - Parsing PDF: Gesture_Control_Drone.pdf
2025-05-29 22:56:32,285 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 22:58:07,873 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,875 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,876 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,876 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,877 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,878 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,878 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:07,879 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,347 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,348 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,348 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,348 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,348 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,349 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,349 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,349 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 22:58:10,349 - INFO - Segmenting text into sections...
2025-05-29 22:58:10,370 - INFO - Extracting key phrases and insights...
2025-05-29 22:58:16,362 - INFO - Building structured dataset...
2025-05-29 22:58:16,480 - INFO - Dataset exported to CSV: output\dataset_20250529_225816.csv
2025-05-29 22:58:16,480 - INFO - Analysis saved to: output\analysis_20250529_225816.json
2025-05-29 22:58:16,481 - INFO - Dataset exported to: output\dataset_20250529_225816.csv
2025-05-29 23:01:26,017 - INFO - SciML Platform initialized
2025-05-29 23:01:26,018 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:01:26,018 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:01:26,018 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 23:01:27,638 - INFO - Download progress: 36.8%
2025-05-29 23:01:28,405 - INFO - Download progress: 73.6%
2025-05-29 23:01:28,820 - INFO - Successfully downloaded PDF to: output\downloaded_20250529_230126.pdf (2,850,797 bytes)
2025-05-29 23:01:28,821 - INFO - Parsing PDF: output\downloaded_20250529_230126.pdf
2025-05-29 23:01:28,973 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 23:02:01,769 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,770 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,771 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,771 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,772 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,772 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,773 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:01,773 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,646 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,647 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,647 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,647 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:02:02,648 - INFO - Segmenting text into sections...
2025-05-29 23:02:02,658 - INFO - Extracting key phrases and insights...
2025-05-29 23:02:04,976 - INFO - Building structured dataset...
2025-05-29 23:02:05,034 - INFO - Dataset exported to CSV: output\dataset_20250529_230204.csv
2025-05-29 23:02:05,034 - INFO - Analysis saved to: output\analysis_20250529_230204.json
2025-05-29 23:02:05,034 - INFO - Dataset exported to: output\dataset_20250529_230204.csv
2025-05-29 23:16:52,519 - INFO - SciML Platform initialized
2025-05-29 23:16:53,718 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 23:16:53,718 - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 23:16:53,880 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:17:00,361 - INFO - SciML Platform initialized
2025-05-29 23:17:00,381 - WARNING -  * Debugger is active!
2025-05-29 23:17:00,399 - INFO -  * Debugger PIN: 121-************-05-29 23:18:49,728 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\gunicorn.conf.py', reloading
2025-05-29 23:18:49,728 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\gunicorn.conf.py', reloading
2025-05-29 23:18:52,309 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:19:00,726 - INFO - SciML Platform initialized
2025-05-29 23:19:00,741 - WARNING -  * Debugger is active!
2025-05-29 23:19:00,746 - INFO -  * Debugger PIN: 121-************-05-29 23:20:28,655 - INFO - SciML Platform initialized
2025-05-29 23:20:29,589 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 23:20:29,589 - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 23:20:29,594 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:20:36,563 - INFO - SciML Platform initialized
2025-05-29 23:20:36,573 - WARNING -  * Debugger is active!
2025-05-29 23:20:36,577 - INFO -  * Debugger PIN: 121-************-05-29 23:21:19,457 - INFO - 127.0.0.1 - - [29/May/2025 23:21:19] "GET / HTTP/1.1" 200 -
2025-05-29 23:21:19,719 - INFO - 127.0.0.1 - - [29/May/2025 23:21:19] "GET /static/js/app.js HTTP/1.1" 200 -
2025-05-29 23:21:20,372 - INFO - 127.0.0.1 - - [29/May/2025 23:21:20] "GET / HTTP/1.1" 200 -
2025-05-29 23:21:20,679 - INFO - 127.0.0.1 - - [29/May/2025 23:21:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-05-29 23:21:21,039 - INFO - 127.0.0.1 - - [29/May/2025 23:21:21] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 23:21:25,351 - INFO - Processing file: C:\Users\<USER>\AppData\Local\Temp\d640d56f-8efd-4a78-94ca-ee1185e39fc4_Gesture_Control_Drone_2.pdf
2025-05-29 23:21:25,353 - INFO - Parsing PDF: C:\Users\<USER>\AppData\Local\Temp\d640d56f-8efd-4a78-94ca-ee1185e39fc4_Gesture_Control_Drone_2.pdf
2025-05-29 23:21:25,353 - INFO - 127.0.0.1 - - [29/May/2025 23:21:25] "POST /api/upload HTTP/1.1" 200 -
2025-05-29 23:21:25,753 - INFO - 127.0.0.1 - - [29/May/2025 23:21:25] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:25,834 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 23:21:27,362 - INFO - 127.0.0.1 - - [29/May/2025 23:21:27] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:29,669 - INFO - 127.0.0.1 - - [29/May/2025 23:21:29] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:31,364 - INFO - 127.0.0.1 - - [29/May/2025 23:21:31] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:33,714 - INFO - 127.0.0.1 - - [29/May/2025 23:21:33] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:35,856 - INFO - 127.0.0.1 - - [29/May/2025 23:21:35] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:37,362 - INFO - 127.0.0.1 - - [29/May/2025 23:21:37] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:39,783 - INFO - 127.0.0.1 - - [29/May/2025 23:21:39] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:41,362 - INFO - 127.0.0.1 - - [29/May/2025 23:21:41] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:43,701 - INFO - 127.0.0.1 - - [29/May/2025 23:21:43] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:45,382 - INFO - 127.0.0.1 - - [29/May/2025 23:21:45] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:47,811 - INFO - 127.0.0.1 - - [29/May/2025 23:21:47] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:49,361 - INFO - 127.0.0.1 - - [29/May/2025 23:21:49] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:51,674 - INFO - 127.0.0.1 - - [29/May/2025 23:21:51] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:53,364 - INFO - 127.0.0.1 - - [29/May/2025 23:21:53] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:55,728 - INFO - 127.0.0.1 - - [29/May/2025 23:21:55] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:57,361 - INFO - 127.0.0.1 - - [29/May/2025 23:21:57] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:21:59,853 - INFO - 127.0.0.1 - - [29/May/2025 23:21:59] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:01,363 - INFO - 127.0.0.1 - - [29/May/2025 23:22:01] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:03,667 - INFO - 127.0.0.1 - - [29/May/2025 23:22:03] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:05,364 - INFO - 127.0.0.1 - - [29/May/2025 23:22:05] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:07,788 - INFO - 127.0.0.1 - - [29/May/2025 23:22:07] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:09,363 - INFO - 127.0.0.1 - - [29/May/2025 23:22:09] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:11,752 - INFO - 127.0.0.1 - - [29/May/2025 23:22:11] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:13,364 - INFO - 127.0.0.1 - - [29/May/2025 23:22:13] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:15,921 - INFO - 127.0.0.1 - - [29/May/2025 23:22:15] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:17,363 - INFO - 127.0.0.1 - - [29/May/2025 23:22:17] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:19,708 - INFO - 127.0.0.1 - - [29/May/2025 23:22:19] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:21,363 - INFO - 127.0.0.1 - - [29/May/2025 23:22:21] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:23,675 - INFO - 127.0.0.1 - - [29/May/2025 23:22:23] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:25,367 - INFO - 127.0.0.1 - - [29/May/2025 23:22:25] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:27,676 - INFO - 127.0.0.1 - - [29/May/2025 23:22:27] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:29,362 - INFO - 127.0.0.1 - - [29/May/2025 23:22:29] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:31,681 - INFO - 127.0.0.1 - - [29/May/2025 23:22:31] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:33,361 - INFO - 127.0.0.1 - - [29/May/2025 23:22:33] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:35,734 - INFO - 127.0.0.1 - - [29/May/2025 23:22:35] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:37,361 - INFO - 127.0.0.1 - - [29/May/2025 23:22:37] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:39,675 - INFO - 127.0.0.1 - - [29/May/2025 23:22:39] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:41,359 - INFO - 127.0.0.1 - - [29/May/2025 23:22:41] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:43,679 - INFO - 127.0.0.1 - - [29/May/2025 23:22:43] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:45,359 - INFO - 127.0.0.1 - - [29/May/2025 23:22:45] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:47,695 - INFO - 127.0.0.1 - - [29/May/2025 23:22:47] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:49,362 - INFO - 127.0.0.1 - - [29/May/2025 23:22:49] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:51,676 - INFO - 127.0.0.1 - - [29/May/2025 23:22:51] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:53,367 - INFO - 127.0.0.1 - - [29/May/2025 23:22:53] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:55,842 - INFO - 127.0.0.1 - - [29/May/2025 23:22:55] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:57,364 - INFO - 127.0.0.1 - - [29/May/2025 23:22:57] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:22:59,782 - INFO - 127.0.0.1 - - [29/May/2025 23:22:59] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:23:01,365 - INFO - 127.0.0.1 - - [29/May/2025 23:23:01] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:23:03,695 - INFO - 127.0.0.1 - - [29/May/2025 23:23:03] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:23:04,862 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,866 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,868 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,869 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,870 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,872 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,873 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,874 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:23:04,912 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\utf_16_be.py', reloading
2025-05-29 23:23:05,387 - INFO - 127.0.0.1 - - [29/May/2025 23:23:05] "GET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1" 200 -
2025-05-29 23:23:09,590 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:23:19,298 - INFO - SciML Platform initialized
2025-05-29 23:23:19,328 - WARNING -  * Debugger is active!
2025-05-29 23:23:19,338 - INFO -  * Debugger PIN: 121-************-05-29 23:23:19,752 - INFO - 127.0.0.1 - - [29/May/2025 23:23:19] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:19,755 - INFO - 127.0.0.1 - - [29/May/2025 23:23:19] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:20,077 - INFO - 127.0.0.1 - - [29/May/2025 23:23:20] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:20,325 - INFO - 127.0.0.1 - - [29/May/2025 23:23:20] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:20,384 - INFO - 127.0.0.1 - - [29/May/2025 23:23:20] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:20,633 - INFO - 127.0.0.1 - - [29/May/2025 23:23:20] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:20,695 - INFO - 127.0.0.1 - - [29/May/2025 23:23:20] "[33mGET /api/status/d640d56f-8efd-4a78-94ca-ee1185e39fc4 HTTP/1.1[0m" 404 -
2025-05-29 23:23:58,433 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:23:58,447 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:00,773 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:24:08,464 - INFO - SciML Platform initialized
2025-05-29 23:24:08,502 - WARNING -  * Debugger is active!
2025-05-29 23:24:08,519 - INFO -  * Debugger PIN: 121-************-05-29 23:24:16,501 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:16,502 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:18,737 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:24:25,514 - INFO - SciML Platform initialized
2025-05-29 23:24:25,524 - WARNING -  * Debugger is active!
2025-05-29 23:24:25,528 - INFO -  * Debugger PIN: 121-************-05-29 23:24:32,401 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:32,402 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:34,716 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:24:43,799 - INFO - SciML Platform initialized
2025-05-29 23:24:43,824 - WARNING -  * Debugger is active!
2025-05-29 23:24:43,837 - INFO -  * Debugger PIN: 121-************-05-29 23:24:51,126 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:51,127 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:24:53,860 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:25:01,008 - INFO - SciML Platform initialized
2025-05-29 23:25:01,042 - WARNING -  * Debugger is active!
2025-05-29 23:25:01,059 - INFO -  * Debugger PIN: 121-************-05-29 23:25:06,567 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:06,568 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:08,266 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:25:16,948 - INFO - SciML Platform initialized
2025-05-29 23:25:16,983 - WARNING -  * Debugger is active!
2025-05-29 23:25:16,998 - INFO -  * Debugger PIN: 121-************-05-29 23:25:24,488 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:24,488 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:26,829 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:25:33,975 - INFO - SciML Platform initialized
2025-05-29 23:25:33,986 - WARNING -  * Debugger is active!
2025-05-29 23:25:33,991 - INFO -  * Debugger PIN: 121-************-05-29 23:25:44,507 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:44,508 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:44,509 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:25:46,052 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:25:54,531 - INFO - SciML Platform initialized
2025-05-29 23:25:54,556 - WARNING -  * Debugger is active!
2025-05-29 23:25:54,568 - INFO -  * Debugger PIN: 121-************-05-29 23:26:20,632 - INFO - SciML Platform initialized
2025-05-29 23:26:21,721 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 23:26:21,722 - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 23:26:21,735 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:26:28,333 - INFO - SciML Platform initialized
2025-05-29 23:26:28,352 - WARNING -  * Debugger is active!
2025-05-29 23:26:28,403 - INFO -  * Debugger PIN: 121-************-05-29 23:27:04,335 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\cleanup.py', reloading
2025-05-29 23:27:04,336 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\cleanup.py', reloading
2025-05-29 23:27:04,337 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\cleanup.py', reloading
2025-05-29 23:27:04,337 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\cleanup.py', reloading
2025-05-29 23:27:04,857 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\Image.py', reloading
2025-05-29 23:27:04,993 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\numpy\\_typing\\_shape.py', reloading
2025-05-29 23:27:04,993 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\numpy\\_typing\\_shape.py', reloading
2025-05-29 23:27:05,042 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\_util.py', reloading
2025-05-29 23:27:05,042 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\_util.py', reloading
2025-05-29 23:27:06,748 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:27:07,236 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:27:15,215 - INFO - SciML Platform initialized
2025-05-29 23:27:15,224 - INFO - SciML Platform initialized
2025-05-29 23:27:15,238 - WARNING -  * Debugger is active!
2025-05-29 23:27:15,247 - WARNING -  * Debugger is active!
2025-05-29 23:27:15,248 - INFO -  * Debugger PIN: 121-************-05-29 23:27:15,258 - INFO -  * Debugger PIN: 121-************-05-29 23:27:21,257 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app_production.py', reloading
2025-05-29 23:27:21,258 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app_production.py', reloading
2025-05-29 23:27:21,258 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app_production.py', reloading
2025-05-29 23:27:21,258 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app_production.py', reloading
2025-05-29 23:27:23,925 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:27:23,962 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:27:30,393 - INFO - SciML Platform initialized
2025-05-29 23:27:30,404 - WARNING -  * Debugger is active!
2025-05-29 23:27:30,409 - INFO -  * Debugger PIN: 121-************-05-29 23:27:30,444 - INFO - SciML Platform initialized
2025-05-29 23:27:30,453 - WARNING -  * Debugger is active!
2025-05-29 23:27:30,458 - INFO -  * Debugger PIN: 121-************-05-29 23:27:39,616 - INFO - 127.0.0.1 - - [29/May/2025 23:27:39] "GET / HTTP/1.1" 200 -
2025-05-29 23:27:40,476 - INFO - 127.0.0.1 - - [29/May/2025 23:27:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-05-29 23:27:54,609 - INFO - 127.0.0.1 - - [29/May/2025 23:27:54] "POST /api/upload HTTP/1.1" 200 -
2025-05-29 23:27:54,635 - INFO - Processing file: C:\Users\<USER>\AppData\Local\Temp\0a341160-69b9-48b2-b9c8-d058ba808729_Gesture_Control_Drone_2.pdf
2025-05-29 23:27:54,636 - INFO - Parsing PDF: C:\Users\<USER>\AppData\Local\Temp\0a341160-69b9-48b2-b9c8-d058ba808729_Gesture_Control_Drone_2.pdf
2025-05-29 23:27:54,725 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\raw_unicode_escape.py', reloading
2025-05-29 23:27:54,771 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\raw_unicode_escape.py', reloading
2025-05-29 23:27:54,987 - INFO - 127.0.0.1 - - [29/May/2025 23:27:54] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:27:55,074 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 23:27:56,916 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:27:56,974 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:28:02,910 - INFO - SciML Platform initialized
2025-05-29 23:28:02,911 - INFO - SciML Platform initialized
2025-05-29 23:28:02,921 - WARNING -  * Debugger is active!
2025-05-29 23:28:02,921 - WARNING -  * Debugger is active!
2025-05-29 23:28:02,930 - INFO -  * Debugger PIN: 121-************-05-29 23:28:02,930 - INFO -  * Debugger PIN: 121-************-05-29 23:28:03,061 - INFO - 127.0.0.1 - - [29/May/2025 23:28:03] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:03,064 - INFO - 127.0.0.1 - - [29/May/2025 23:28:03] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:03,386 - INFO - 127.0.0.1 - - [29/May/2025 23:28:03] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:03,631 - INFO - 127.0.0.1 - - [29/May/2025 23:28:03] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:04,661 - INFO - 127.0.0.1 - - [29/May/2025 23:28:04] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:06,953 - INFO - 127.0.0.1 - - [29/May/2025 23:28:06] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:08,651 - INFO - 127.0.0.1 - - [29/May/2025 23:28:08] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:10,962 - INFO - 127.0.0.1 - - [29/May/2025 23:28:10] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:12,649 - INFO - 127.0.0.1 - - [29/May/2025 23:28:12] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:14,961 - INFO - 127.0.0.1 - - [29/May/2025 23:28:14] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:16,649 - INFO - 127.0.0.1 - - [29/May/2025 23:28:16] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:18,971 - INFO - 127.0.0.1 - - [29/May/2025 23:28:18] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:20,615 - INFO - 127.0.0.1 - - [29/May/2025 23:28:20] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:22,922 - INFO - 127.0.0.1 - - [29/May/2025 23:28:22] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:24,657 - INFO - 127.0.0.1 - - [29/May/2025 23:28:24] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:26,961 - INFO - 127.0.0.1 - - [29/May/2025 23:28:26] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:28,663 - INFO - 127.0.0.1 - - [29/May/2025 23:28:28] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:30,922 - INFO - 127.0.0.1 - - [29/May/2025 23:28:30] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:32,614 - INFO - 127.0.0.1 - - [29/May/2025 23:28:32] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:34,971 - INFO - 127.0.0.1 - - [29/May/2025 23:28:34] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:36,653 - INFO - 127.0.0.1 - - [29/May/2025 23:28:36] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:37,910 - INFO - 127.0.0.1 - - [29/May/2025 23:28:37] "GET / HTTP/1.1" 200 -
2025-05-29 23:28:38,516 - INFO - 127.0.0.1 - - [29/May/2025 23:28:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-05-29 23:28:38,700 - INFO - 127.0.0.1 - - [29/May/2025 23:28:38] "GET /api/status/0a341160-69b9-48b2-b9c8-d058ba808729 HTTP/1.1" 200 -
2025-05-29 23:28:45,237 - INFO - 127.0.0.1 - - [29/May/2025 23:28:45] "POST /api/upload HTTP/1.1" 200 -
2025-05-29 23:28:45,282 - INFO - Processing file: C:\Users\<USER>\AppData\Local\Temp\67d29132-084b-4fa8-a83d-aa68160ec63f_Gesture_Control_Drone_2.pdf
2025-05-29 23:28:45,283 - INFO - Parsing PDF: C:\Users\<USER>\AppData\Local\Temp\67d29132-084b-4fa8-a83d-aa68160ec63f_Gesture_Control_Drone_2.pdf
2025-05-29 23:28:45,533 - INFO - 127.0.0.1 - - [29/May/2025 23:28:45] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:45,604 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-05-29 23:28:47,251 - INFO - 127.0.0.1 - - [29/May/2025 23:28:47] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:49,957 - INFO - 127.0.0.1 - - [29/May/2025 23:28:49] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:52,056 - INFO - 127.0.0.1 - - [29/May/2025 23:28:52] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:53,656 - INFO - 127.0.0.1 - - [29/May/2025 23:28:53] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:55,966 - INFO - 127.0.0.1 - - [29/May/2025 23:28:55] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:57,646 - INFO - 127.0.0.1 - - [29/May/2025 23:28:57] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:28:59,958 - INFO - 127.0.0.1 - - [29/May/2025 23:28:59] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:01,658 - INFO - 127.0.0.1 - - [29/May/2025 23:29:01] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:03,972 - INFO - 127.0.0.1 - - [29/May/2025 23:29:03] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:05,668 - INFO - 127.0.0.1 - - [29/May/2025 23:29:05] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:07,974 - INFO - 127.0.0.1 - - [29/May/2025 23:29:07] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:09,665 - INFO - 127.0.0.1 - - [29/May/2025 23:29:09] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:11,980 - INFO - 127.0.0.1 - - [29/May/2025 23:29:11] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:13,647 - INFO - 127.0.0.1 - - [29/May/2025 23:29:13] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:15,965 - INFO - 127.0.0.1 - - [29/May/2025 23:29:15] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:17,650 - INFO - 127.0.0.1 - - [29/May/2025 23:29:17] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:19,964 - INFO - 127.0.0.1 - - [29/May/2025 23:29:19] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:21,659 - INFO - 127.0.0.1 - - [29/May/2025 23:29:21] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:23,959 - INFO - 127.0.0.1 - - [29/May/2025 23:29:23] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:25,654 - INFO - 127.0.0.1 - - [29/May/2025 23:29:25] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:27,959 - INFO - 127.0.0.1 - - [29/May/2025 23:29:27] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:29,652 - INFO - 127.0.0.1 - - [29/May/2025 23:29:29] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:29,957 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,958 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,959 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,959 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,959 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,959 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,959 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:29,960 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,064 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,065 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,065 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,065 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,065 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,066 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,066 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,066 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:29:31,067 - INFO - Segmenting text into sections...
2025-05-29 23:29:31,077 - INFO - Extracting key phrases and insights...
2025-05-29 23:29:31,961 - INFO - 127.0.0.1 - - [29/May/2025 23:29:31] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:33,477 - INFO - 127.0.0.1 - - [29/May/2025 23:29:33] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:29:33,540 - INFO - Building structured dataset...
2025-05-29 23:29:33,600 - INFO - Dataset exported to CSV: output\dataset_20250529_232933.csv
2025-05-29 23:29:33,600 - INFO - Analysis saved to: output\analysis_20250529_232933.json
2025-05-29 23:29:33,602 - INFO - Dataset exported to: output\dataset_20250529_232933.csv
2025-05-29 23:29:35,643 - INFO - 127.0.0.1 - - [29/May/2025 23:29:35] "GET /api/status/67d29132-084b-4fa8-a83d-aa68160ec63f HTTP/1.1" 200 -
2025-05-29 23:30:34,788 - INFO - 127.0.0.1 - - [29/May/2025 23:30:34] "POST /api/analyze-url HTTP/1.1" 200 -
2025-05-29 23:30:34,829 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:30:34,831 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:30:34,831 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 23:30:34,837 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\netrc.py', reloading
2025-05-29 23:30:34,841 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\netrc.py', reloading
2025-05-29 23:30:35,105 - INFO - 127.0.0.1 - - [29/May/2025 23:30:35] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:37,271 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:30:37,925 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:30:45,171 - INFO - SciML Platform initialized
2025-05-29 23:30:45,190 - WARNING -  * Debugger is active!
2025-05-29 23:30:45,198 - INFO -  * Debugger PIN: 121-************-05-29 23:30:47,203 - INFO - SciML Platform initialized
2025-05-29 23:30:47,234 - WARNING -  * Debugger is active!
2025-05-29 23:30:47,250 - INFO -  * Debugger PIN: 121-************-05-29 23:30:47,594 - INFO - 127.0.0.1 - - [29/May/2025 23:30:47] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:47,598 - INFO - 127.0.0.1 - - [29/May/2025 23:30:47] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:47,916 - INFO - 127.0.0.1 - - [29/May/2025 23:30:47] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:48,166 - INFO - 127.0.0.1 - - [29/May/2025 23:30:48] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:48,227 - INFO - 127.0.0.1 - - [29/May/2025 23:30:48] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:48,476 - INFO - 127.0.0.1 - - [29/May/2025 23:30:48] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:49,659 - INFO - 127.0.0.1 - - [29/May/2025 23:30:49] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:51,955 - INFO - 127.0.0.1 - - [29/May/2025 23:30:51] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:53,660 - INFO - 127.0.0.1 - - [29/May/2025 23:30:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:55,969 - INFO - 127.0.0.1 - - [29/May/2025 23:30:55] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:57,659 - INFO - 127.0.0.1 - - [29/May/2025 23:30:57] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:30:59,962 - INFO - 127.0.0.1 - - [29/May/2025 23:30:59] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:01,653 - INFO - 127.0.0.1 - - [29/May/2025 23:31:01] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:03,963 - INFO - 127.0.0.1 - - [29/May/2025 23:31:03] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:05,652 - INFO - 127.0.0.1 - - [29/May/2025 23:31:05] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:07,106 - INFO - 127.0.0.1 - - [29/May/2025 23:31:07] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:08,799 - INFO - 127.0.0.1 - - [29/May/2025 23:31:08] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:11,969 - INFO - 127.0.0.1 - - [29/May/2025 23:31:11] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:13,651 - INFO - 127.0.0.1 - - [29/May/2025 23:31:13] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:15,964 - INFO - 127.0.0.1 - - [29/May/2025 23:31:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:17,661 - INFO - 127.0.0.1 - - [29/May/2025 23:31:17] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:19,969 - INFO - 127.0.0.1 - - [29/May/2025 23:31:19] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:21,655 - INFO - 127.0.0.1 - - [29/May/2025 23:31:21] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:23,966 - INFO - 127.0.0.1 - - [29/May/2025 23:31:23] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:25,652 - INFO - 127.0.0.1 - - [29/May/2025 23:31:25] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:27,975 - INFO - 127.0.0.1 - - [29/May/2025 23:31:27] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:29,649 - INFO - 127.0.0.1 - - [29/May/2025 23:31:29] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:31,957 - INFO - 127.0.0.1 - - [29/May/2025 23:31:31] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:33,653 - INFO - 127.0.0.1 - - [29/May/2025 23:31:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:35,957 - INFO - 127.0.0.1 - - [29/May/2025 23:31:35] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:37,652 - INFO - 127.0.0.1 - - [29/May/2025 23:31:37] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:39,969 - INFO - 127.0.0.1 - - [29/May/2025 23:31:39] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:41,649 - INFO - 127.0.0.1 - - [29/May/2025 23:31:41] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:43,958 - INFO - 127.0.0.1 - - [29/May/2025 23:31:43] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:44,794 - INFO - 127.0.0.1 - - [29/May/2025 23:31:44] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:47,106 - INFO - 127.0.0.1 - - [29/May/2025 23:31:47] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:49,653 - INFO - 127.0.0.1 - - [29/May/2025 23:31:49] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:51,960 - INFO - 127.0.0.1 - - [29/May/2025 23:31:51] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:53,654 - INFO - 127.0.0.1 - - [29/May/2025 23:31:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:55,966 - INFO - 127.0.0.1 - - [29/May/2025 23:31:55] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:57,649 - INFO - 127.0.0.1 - - [29/May/2025 23:31:57] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:31:59,968 - INFO - 127.0.0.1 - - [29/May/2025 23:31:59] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:01,653 - INFO - 127.0.0.1 - - [29/May/2025 23:32:01] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:03,962 - INFO - 127.0.0.1 - - [29/May/2025 23:32:03] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:04,981 - INFO - 127.0.0.1 - - [29/May/2025 23:32:04] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:07,971 - INFO - 127.0.0.1 - - [29/May/2025 23:32:07] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:09,709 - INFO - 127.0.0.1 - - [29/May/2025 23:32:09] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:11,963 - INFO - 127.0.0.1 - - [29/May/2025 23:32:11] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:13,657 - INFO - 127.0.0.1 - - [29/May/2025 23:32:13] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:15,963 - INFO - 127.0.0.1 - - [29/May/2025 23:32:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:17,654 - INFO - 127.0.0.1 - - [29/May/2025 23:32:17] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:19,969 - INFO - 127.0.0.1 - - [29/May/2025 23:32:19] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:21,656 - INFO - 127.0.0.1 - - [29/May/2025 23:32:21] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:23,968 - INFO - 127.0.0.1 - - [29/May/2025 23:32:23] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:25,653 - INFO - 127.0.0.1 - - [29/May/2025 23:32:25] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:27,960 - INFO - 127.0.0.1 - - [29/May/2025 23:32:27] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:29,651 - INFO - 127.0.0.1 - - [29/May/2025 23:32:29] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:31,969 - INFO - 127.0.0.1 - - [29/May/2025 23:32:31] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:33,657 - INFO - 127.0.0.1 - - [29/May/2025 23:32:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:35,964 - INFO - 127.0.0.1 - - [29/May/2025 23:32:35] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:37,649 - INFO - 127.0.0.1 - - [29/May/2025 23:32:37] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:39,961 - INFO - 127.0.0.1 - - [29/May/2025 23:32:39] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:41,611 - INFO - 127.0.0.1 - - [29/May/2025 23:32:41] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:43,101 - INFO - 127.0.0.1 - - [29/May/2025 23:32:43] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:45,648 - INFO - 127.0.0.1 - - [29/May/2025 23:32:45] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:47,964 - INFO - 127.0.0.1 - - [29/May/2025 23:32:47] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:49,658 - INFO - 127.0.0.1 - - [29/May/2025 23:32:49] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:51,967 - INFO - 127.0.0.1 - - [29/May/2025 23:32:51] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:53,657 - INFO - 127.0.0.1 - - [29/May/2025 23:32:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:55,967 - INFO - 127.0.0.1 - - [29/May/2025 23:32:55] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:57,655 - INFO - 127.0.0.1 - - [29/May/2025 23:32:57] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:32:59,116 - INFO - 127.0.0.1 - - [29/May/2025 23:32:59] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:01,654 - INFO - 127.0.0.1 - - [29/May/2025 23:33:01] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:03,958 - INFO - 127.0.0.1 - - [29/May/2025 23:33:03] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:04,780 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:04,781 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:05,657 - INFO - 127.0.0.1 - - [29/May/2025 23:33:05] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:07,151 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:33:14,876 - INFO - SciML Platform initialized
2025-05-29 23:33:14,900 - WARNING -  * Debugger is active!
2025-05-29 23:33:14,912 - INFO -  * Debugger PIN: 121-************-05-29 23:33:15,251 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:15,255 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:15,258 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:15,264 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:15,586 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:15,833 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:15,894 - INFO - 127.0.0.1 - - [29/May/2025 23:33:15] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:17,653 - INFO - 127.0.0.1 - - [29/May/2025 23:33:17] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:19,965 - INFO - 127.0.0.1 - - [29/May/2025 23:33:19] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:21,379 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:21,381 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:21,655 - INFO - 127.0.0.1 - - [29/May/2025 23:33:21] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:23,479 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:33:32,345 - INFO - SciML Platform initialized
2025-05-29 23:33:32,381 - WARNING -  * Debugger is active!
2025-05-29 23:33:32,395 - INFO -  * Debugger PIN: 121-************-05-29 23:33:32,812 - INFO - 127.0.0.1 - - [29/May/2025 23:33:32] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:32,820 - INFO - 127.0.0.1 - - [29/May/2025 23:33:32] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:33,134 - INFO - 127.0.0.1 - - [29/May/2025 23:33:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:33,398 - INFO - 127.0.0.1 - - [29/May/2025 23:33:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:33,445 - INFO - 127.0.0.1 - - [29/May/2025 23:33:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:33,708 - INFO - 127.0.0.1 - - [29/May/2025 23:33:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:35,648 - INFO - 127.0.0.1 - - [29/May/2025 23:33:35] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:37,971 - INFO - 127.0.0.1 - - [29/May/2025 23:33:37] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:39,208 - INFO - 127.0.0.1 - - [29/May/2025 23:33:39] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:39,470 - INFO - 127.0.0.1 - - [29/May/2025 23:33:39] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:39,659 - INFO - 127.0.0.1 - - [29/May/2025 23:33:39] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:41,306 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:41,307 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:33:41,966 - INFO - 127.0.0.1 - - [29/May/2025 23:33:41] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:43,844 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:33:52,658 - INFO - SciML Platform initialized
2025-05-29 23:33:52,681 - WARNING -  * Debugger is active!
2025-05-29 23:33:52,692 - INFO -  * Debugger PIN: 121-************-05-29 23:33:53,004 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:53,007 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:53,012 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:33:53,314 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:53,578 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:53,635 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:53,885 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:53,951 - INFO - 127.0.0.1 - - [29/May/2025 23:33:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:55,653 - INFO - 127.0.0.1 - - [29/May/2025 23:33:55] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:57,961 - INFO - 127.0.0.1 - - [29/May/2025 23:33:57] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:33:59,653 - INFO - 127.0.0.1 - - [29/May/2025 23:33:59] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:34:05,609 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:05,609 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:05,610 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:08,228 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:34:15,758 - INFO - SciML Platform initialized
2025-05-29 23:34:15,798 - WARNING -  * Debugger is active!
2025-05-29 23:34:15,804 - INFO -  * Debugger PIN: 121-************-05-29 23:34:29,488 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:29,489 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:29,489 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\app.py', reloading
2025-05-29 23:34:32,023 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:34:40,082 - INFO - SciML Platform initialized
2025-05-29 23:34:40,114 - WARNING -  * Debugger is active!
2025-05-29 23:34:40,129 - INFO -  * Debugger PIN: 121-************-05-29 23:34:40,498 - INFO - 127.0.0.1 - - [29/May/2025 23:34:40] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:35:25,726 - INFO - SciML Platform initialized
2025-05-29 23:35:26,666 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 23:35:26,666 - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 23:35:26,676 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:35:32,818 - INFO - SciML Platform initialized
2025-05-29 23:35:32,828 - WARNING -  * Debugger is active!
2025-05-29 23:35:32,837 - INFO -  * Debugger PIN: 121-************-05-29 23:35:33,660 - INFO - 127.0.0.1 - - [29/May/2025 23:35:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:36:11,279 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:36:11,279 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:36:11,279 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:36:11,279 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:36:11,795 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\packaging\\version.py', reloading
2025-05-29 23:36:11,826 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\ImageMode.py', reloading
2025-05-29 23:36:11,828 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\ImagePalette.py', reloading
2025-05-29 23:36:11,829 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\PyAccess.py', reloading
2025-05-29 23:36:11,830 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\PIL\\ImageColor.py', reloading
2025-05-29 23:36:11,855 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\pytesseract\\__init__.py', reloading
2025-05-29 23:36:11,856 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python39\\site-packages\\pytesseract\\pytesseract.py', reloading
2025-05-29 23:36:12,679 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:36:12,917 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:36:20,274 - INFO - SciML Platform initialized
2025-05-29 23:36:20,283 - WARNING -  * Debugger is active!
2025-05-29 23:36:20,293 - INFO -  * Debugger PIN: 121-************-05-29 23:36:20,323 - INFO - SciML Platform initialized
2025-05-29 23:36:20,332 - WARNING -  * Debugger is active!
2025-05-29 23:36:20,349 - INFO -  * Debugger PIN: 121-************-05-29 23:36:27,322 - INFO - 127.0.0.1 - - [29/May/2025 23:36:27] "GET /health HTTP/1.1" 200 -
2025-05-29 23:36:29,371 - INFO - 127.0.0.1 - - [29/May/2025 23:36:29] "POST /api/analyze-url HTTP/1.1" 200 -
2025-05-29 23:36:29,437 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:36:29,437 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:36:29,437 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 23:36:30,994 - INFO - Download progress: 36.8%
2025-05-29 23:36:31,581 - INFO - Download progress: 73.6%
2025-05-29 23:36:32,010 - INFO - Successfully downloaded PDF to: output\downloaded_20250529_233629.pdf (2,850,797 bytes)
2025-05-29 23:36:32,013 - INFO - Parsing PDF: output\downloaded_20250529_233629.pdf
2025-05-29 23:36:32,837 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,838 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,839 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,840 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,841 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,841 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,842 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:32,843 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,647 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,648 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,649 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,649 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,649 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:36:33,649 - INFO - Segmenting text into sections...
2025-05-29 23:36:33,658 - INFO - Extracting key phrases and insights...
2025-05-29 23:36:33,968 - INFO - 127.0.0.1 - - [29/May/2025 23:36:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:36:35,945 - INFO - Building structured dataset...
2025-05-29 23:36:35,999 - INFO - Dataset exported to CSV: output\dataset_20250529_233635.csv
2025-05-29 23:36:35,999 - INFO - Analysis saved to: output\analysis_20250529_233635.json
2025-05-29 23:36:35,999 - INFO - Dataset exported to: output\dataset_20250529_233635.csv
2025-05-29 23:36:41,537 - INFO - 127.0.0.1 - - [29/May/2025 23:36:41] "GET /api/status/0db5d5b7-84ab-4bea-9f24-d56fdde600ab HTTP/1.1" 200 -
2025-05-29 23:36:43,698 - INFO - 127.0.0.1 - - [29/May/2025 23:36:43] "GET /api/results/0db5d5b7-84ab-4bea-9f24-d56fdde600ab HTTP/1.1" 200 -
2025-05-29 23:36:45,812 - INFO - 127.0.0.1 - - [29/May/2025 23:36:45] "GET /api/debug/tasks HTTP/1.1" 200 -
2025-05-29 23:36:53,154 - INFO - 127.0.0.1 - - [29/May/2025 23:36:53] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:36:53,419 - INFO - 127.0.0.1 - - [29/May/2025 23:36:53] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:36:54,337 - INFO - 127.0.0.1 - - [29/May/2025 23:36:54] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:36:54,672 - INFO - 127.0.0.1 - - [29/May/2025 23:36:54] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:37:20,081 - INFO - 127.0.0.1 - - [29/May/2025 23:37:20] "GET /health HTTP/1.1" 200 -
2025-05-29 23:37:22,121 - INFO - 127.0.0.1 - - [29/May/2025 23:37:22] "POST /api/analyze-url HTTP/1.1" 200 -
2025-05-29 23:37:22,177 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:37:22,177 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:37:22,177 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 23:37:23,508 - INFO - Download progress: 36.8%
2025-05-29 23:37:24,059 - INFO - Download progress: 73.6%
2025-05-29 23:37:24,488 - INFO - Successfully downloaded PDF to: output\downloaded_20250529_233722.pdf (2,850,797 bytes)
2025-05-29 23:37:24,489 - INFO - Parsing PDF: output\downloaded_20250529_233722.pdf
2025-05-29 23:37:25,322 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,324 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,325 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,325 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,326 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,327 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,328 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:25,329 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,536 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,537 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,537 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,537 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,538 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,538 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,538 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,538 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:37:26,539 - INFO - Segmenting text into sections...
2025-05-29 23:37:26,547 - INFO - Extracting key phrases and insights...
2025-05-29 23:37:28,092 - INFO - Building structured dataset...
2025-05-29 23:37:28,141 - INFO - Dataset exported to CSV: output\dataset_20250529_233728.csv
2025-05-29 23:37:28,141 - INFO - Analysis saved to: output\analysis_20250529_233728.json
2025-05-29 23:37:28,142 - INFO - Dataset exported to: output\dataset_20250529_233728.csv
2025-05-29 23:37:33,653 - INFO - 127.0.0.1 - - [29/May/2025 23:37:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:37:34,239 - INFO - 127.0.0.1 - - [29/May/2025 23:37:34] "GET /api/status/8c2017a9-5a46-4d88-8a54-7530d13492fa HTTP/1.1" 200 -
2025-05-29 23:37:36,391 - INFO - 127.0.0.1 - - [29/May/2025 23:37:36] "GET /api/results/8c2017a9-5a46-4d88-8a54-7530d13492fa HTTP/1.1" 200 -
2025-05-29 23:37:38,487 - INFO - 127.0.0.1 - - [29/May/2025 23:37:38] "GET /api/debug/tasks HTTP/1.1" 200 -
2025-05-29 23:38:28,795 - INFO - 127.0.0.1 - - [29/May/2025 23:38:28] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:38:29,061 - INFO - 127.0.0.1 - - [29/May/2025 23:38:29] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:38:33,966 - INFO - 127.0.0.1 - - [29/May/2025 23:38:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:39:33,975 - INFO - 127.0.0.1 - - [29/May/2025 23:39:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:39:35,006 - INFO - 127.0.0.1 - - [29/May/2025 23:39:35] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:39:35,255 - INFO - 127.0.0.1 - - [29/May/2025 23:39:35] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:39:48,004 - INFO - 127.0.0.1 - - [29/May/2025 23:39:48] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:39:48,252 - INFO - 127.0.0.1 - - [29/May/2025 23:39:48] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:39:58,671 - INFO - 127.0.0.1 - - [29/May/2025 23:39:58] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:40:28,988 - INFO - 127.0.0.1 - - [29/May/2025 23:40:28] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:40:33,953 - INFO - 127.0.0.1 - - [29/May/2025 23:40:33] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:40:53,901 - INFO - 127.0.0.1 - - [29/May/2025 23:40:53] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:40:54,372 - INFO - SciML Platform initialized
2025-05-29 23:40:54,796 - INFO - 127.0.0.1 - - [29/May/2025 23:40:54] "GET /api/status/c92b297d-d1a0-4ab6-ae4a-37f3d624f5f6 HTTP/1.1" 200 -
2025-05-29 23:40:55,322 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 23:40:55,322 - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 23:40:55,343 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:40:56,448 - INFO - 127.0.0.1 - - [29/May/2025 23:40:56] "GET / HTTP/1.1" 200 -
2025-05-29 23:40:57,360 - INFO - 127.0.0.1 - - [29/May/2025 23:40:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-05-29 23:41:08,292 - INFO - SciML Platform initialized
2025-05-29 23:41:08,317 - WARNING -  * Debugger is active!
2025-05-29 23:41:08,332 - INFO -  * Debugger PIN: 121-************-05-29 23:41:13,031 - INFO - 127.0.0.1 - - [29/May/2025 23:41:13] "[33mGET /api/health HTTP/1.1[0m" 404 -
2025-05-29 23:41:18,820 - INFO - 127.0.0.1 - - [29/May/2025 23:41:18] "POST /api/analyze-url HTTP/1.1" 200 -
2025-05-29 23:41:18,866 - INFO - Processing URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:41:18,867 - INFO - Downloading from URL: https://arxiv.org/abs/2505.17303
2025-05-29 23:41:18,867 - INFO - Converted ArXiv URL to PDF: https://arxiv.org/pdf/2505.17303.pdf
2025-05-29 23:41:19,079 - INFO - 127.0.0.1 - - [29/May/2025 23:41:19] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:20,839 - INFO - 127.0.0.1 - - [29/May/2025 23:41:20] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:22,141 - INFO - Download progress: 36.8%
2025-05-29 23:41:22,712 - INFO - Download progress: 73.6%
2025-05-29 23:41:23,591 - INFO - Successfully downloaded PDF to: output\downloaded_20250529_234118.pdf (2,850,797 bytes)
2025-05-29 23:41:23,595 - INFO - Parsing PDF: output\downloaded_20250529_234118.pdf
2025-05-29 23:41:23,961 - INFO - 127.0.0.1 - - [29/May/2025 23:41:23] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:25,617 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,619 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,620 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,622 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,627 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,629 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,634 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,639 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:25,671 - INFO - 127.0.0.1 - - [29/May/2025 23:41:25] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:27,474 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,475 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,475 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,476 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,476 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,476 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,476 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,476 - WARNING - CropBox missing from /Page, defaulting to MediaBox
2025-05-29 23:41:27,477 - INFO - Segmenting text into sections...
2025-05-29 23:41:27,485 - INFO - Extracting key phrases and insights...
2025-05-29 23:41:27,509 - INFO - 127.0.0.1 - - [29/May/2025 23:41:27] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:29,385 - INFO - Building structured dataset...
2025-05-29 23:41:29,461 - INFO - Dataset exported to CSV: output\dataset_20250529_234129.csv
2025-05-29 23:41:29,461 - INFO - Analysis saved to: output\analysis_20250529_234129.json
2025-05-29 23:41:29,461 - INFO - Dataset exported to: output\dataset_20250529_234129.csv
2025-05-29 23:41:29,741 - INFO - 127.0.0.1 - - [29/May/2025 23:41:29] "GET /api/status/e4cbbae1-25dd-4e95-8bee-39a3d277fa90 HTTP/1.1" 200 -
2025-05-29 23:41:42,921 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:41:42,921 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:41:42,925 - INFO -  * Detected change in 'D:\\SciML_Project\\AI_tests\\test_url.py', reloading
2025-05-29 23:41:45,099 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:41:45,482 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:41:45,958 - INFO -  * Restarting with watchdog (windowsapi)
2025-05-29 23:41:54,103 - INFO - SciML Platform initialized
2025-05-29 23:41:54,103 - INFO - SciML Platform initialized
2025-05-29 23:41:54,104 - INFO - SciML Platform initialized
2025-05-29 23:41:54,117 - WARNING -  * Debugger is active!
2025-05-29 23:41:54,117 - WARNING -  * Debugger is active!
2025-05-29 23:41:54,117 - WARNING -  * Debugger is active!
2025-05-29 23:41:54,127 - INFO -  * Debugger PIN: 121-************-05-29 23:41:54,127 - INFO -  * Debugger PIN: 121-************-05-29 23:41:54,129 - INFO -  * Debugger PIN: 121-************-06-11 15:59:37,552 - INFO - SciML Platform initialized
2025-06-11 15:59:38,920 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 15:59:38,921 - INFO - [33mPress CTRL+C to quit[0m
2025-06-11 15:59:39,109 - INFO -  * Restarting with watchdog (windowsapi)
2025-06-11 15:59:45,648 - INFO - SciML Platform initialized
2025-06-11 15:59:45,673 - WARNING -  * Debugger is active!
2025-06-11 15:59:45,713 - INFO -  * Debugger PIN: 121-************-06-11 16:00:19,292 - INFO - 127.0.0.1 - - [11/Jun/2025 16:00:19] "GET / HTTP/1.1" 200 -
2025-06-11 16:00:20,146 - INFO - 127.0.0.1 - - [11/Jun/2025 16:00:20] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 16:00:22,108 - INFO - 127.0.0.1 - - [11/Jun/2025 16:00:22] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 16:01:09,478 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:09] "POST /api/upload HTTP/1.1" 200 -
2025-06-11 16:01:09,525 - INFO - Processing file: C:\Users\<USER>\AppData\Local\Temp\5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1_Gesture_Control_Drone_2.pdf
2025-06-11 16:01:09,528 - INFO - Parsing PDF: C:\Users\<USER>\AppData\Local\Temp\5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1_Gesture_Control_Drone_2.pdf
2025-06-11 16:01:09,630 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\raw_unicode_escape.py', reloading
2025-06-11 16:01:09,631 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\raw_unicode_escape.py', reloading
2025-06-11 16:01:09,681 - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.9_3.9.3568.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\raw_unicode_escape.py', reloading
2025-06-11 16:01:09,879 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:09] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:10,032 - WARNING - Using CPU. Note: This module is much faster with a GPU.
2025-06-11 16:01:11,690 - INFO -  * Restarting with watchdog (windowsapi)
2025-06-11 16:01:22,348 - INFO - SciML Platform initialized
2025-06-11 16:01:22,366 - WARNING -  * Debugger is active!
2025-06-11 16:01:22,389 - INFO -  * Debugger PIN: 121-************-06-11 16:01:22,802 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:22] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:23,127 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:23,376 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:23,438 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:23,687 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:23,748 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:24,012 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:24] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:25,491 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:25] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:27,803 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:27] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:29,490 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:29] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:31,803 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:31] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:33,909 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:33] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:36,218 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:36] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:37,488 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:37] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:39,841 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:39] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:41,489 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:41] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:43,796 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:43] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:45,488 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:45] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:47,790 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:47] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:49,488 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:49] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:51,808 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:51] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:53,486 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:53] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:55,795 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:55] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:57,800 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:57] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:01:59,488 - INFO - 127.0.0.1 - - [11/Jun/2025 16:01:59] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:01,795 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:01] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:03,491 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:03] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:06,225 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:06] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:08,213 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:08] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:09,916 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:09] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:12,224 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:12] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:13,910 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:13] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:16,230 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:16] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:17,798 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:17] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:19,489 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:19] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:21,806 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:21] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:23,490 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:25,801 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:25] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:27,794 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:27] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:29,490 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:29] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:31,799 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:31] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:33,912 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:33] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:36,228 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:36] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:38,221 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:38] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:39,486 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:39] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:41,804 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:41] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:43,486 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:43] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:45,794 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:45] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:47,795 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:47] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:49,484 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:49] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:51,791 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:51] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:53,484 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:53] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:55,796 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:55] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:58,218 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:58] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:02:59,911 - INFO - 127.0.0.1 - - [11/Jun/2025 16:02:59] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:02,231 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:02] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:03,913 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:03] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:06,218 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:06] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:08,218 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:08] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:09,911 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:09] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:12,215 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:12] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:13,920 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:13] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:16,223 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:16] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:17,916 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:17] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:20,221 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:20] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:22,220 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:22] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:23,917 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:23] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:26,214 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:26] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
2025-06-11 16:03:27,905 - INFO - 127.0.0.1 - - [11/Jun/2025 16:03:27] "GET /api/status/5c3ffbfc-abdb-465c-9c0a-d08b28cc00e1 HTTP/1.1" 200 -
