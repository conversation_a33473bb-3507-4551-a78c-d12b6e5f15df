/* Insights.css - Styles for the Insights component */

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Card hover effect */
.insight-card {
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Image zoom effect */
.insight-image {
  transition: transform 0.5s ease;
}

.insight-card:hover .insight-image {
  transform: scale(1.1);
}

/* Category pill glow effect */
.category-pill {
  position: relative;
}

.category-pill::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(to right, #00b5ad, #2f4b7c);
  border-radius: 9999px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-pill:hover::after {
  opacity: 0.5;
}

/* Read more link animation */
.read-more {
  position: relative;
  transition: all 0.3s ease;
}

.read-more::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.read-more:hover::after {
  width: 100%;
}

/* Filter button animation */
.filter-button {
  position: relative;
  overflow: hidden;
}

.filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.filter-button:hover::before {
  left: 100%;
}

/* Search input focus effect */
.search-input:focus {
  box-shadow: 0 0 0 2px rgba(0, 181, 173, 0.5);
}

/* Pagination button hover effect */
.pagination-button {
  transition: all 0.3s ease;
}

.pagination-button:hover {
  transform: translateY(-2px);
}

/* No results animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.no-results-icon {
  animation: pulse 2s infinite;
}
