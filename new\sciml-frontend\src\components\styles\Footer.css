/* Footer.css - Styles for the Footer component */

/* Grid pattern overlay */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Animations for hover effects */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 181, 173, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 181, 173, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Custom scrollbar for the footer */
.footer-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.footer-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.footer-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #00b5ad, #2f4b7c);
  border-radius: 10px;
}

.footer-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #00b5ad, #003f5c);
}
