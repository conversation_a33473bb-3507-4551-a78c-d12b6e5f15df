/* APIs.css - Styles for the APIs component */

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* API card hover effect */
.api-card {
  transition: all 0.3s ease;
}

.api-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Method badge colors */
.method-badge.get {
  background-color: rgba(16, 185, 129, 0.8);
}

.method-badge.post {
  background-color: rgba(59, 130, 246, 0.8);
}

.method-badge.put {
  background-color: rgba(245, 158, 11, 0.8);
}

.method-badge.delete {
  background-color: rgba(239, 68, 68, 0.8);
}

/* Code block styling */
.code-block {
  background-color: rgba(31, 41, 55, 0.8);
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Copy button animation */
@keyframes copy-success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.copy-success {
  animation: copy-success 0.3s ease-in-out;
}

/* Tab hover effect */
.tab-hover {
  position: relative;
  overflow: hidden;
}

.tab-hover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--highlight-color, #00b5ad);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.tab-hover:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Endpoint expansion animation */
.endpoint-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
}

.endpoint-content.expanded {
  max-height: 2000px;
}

/* Parameter table styling */
.parameter-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.parameter-table th,
.parameter-table td {
  padding: 0.75rem;
  text-align: left;
}

.parameter-table th {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.parameter-table tr {
  transition: background-color 0.2s ease;
}

.parameter-table tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Button glow effect */
.btn-glow:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.6);
}
