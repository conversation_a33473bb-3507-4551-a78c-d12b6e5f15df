// SciML Platform - Enhanced Frontend JavaScript

let currentTaskId = null;
let statusCheckInterval = null;

// Tab switching for upload tabs
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Results tab switching
function switchResultsTab(tabName) {
    // Remove active class from all results tab buttons and contents
    document.querySelectorAll('.results-tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.results-tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab
    document.querySelector(`[onclick="switchResultsTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// File upload handling
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        showError('Please select a PDF file.');
        return;
    }
    
    if (file.size > 50 * 1024 * 1024) { // 50MB
        showError('File size must be less than 50MB.');
        return;
    }
    
    uploadFile(file);
}

// Drag and drop handling
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.upload-area');
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.name.toLowerCase().endsWith('.pdf')) {
                uploadFile(file);
            } else {
                showError('Please drop a PDF file.');
            }
        }
    });
});

// Upload file to server
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showProgress();
        updateProgress(10, 'Uploading file...');
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            currentTaskId = result.task_id;
            startStatusCheck();
        } else {
            showError(result.error || 'Upload failed');
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// URL analysis handling
async function handleUrlAnalysis() {
    const urlInput = document.getElementById('urlInput');
    const url = urlInput.value.trim();
    
    if (!url) {
        showError('Please enter a URL.');
        return;
    }
    
    try {
        showProgress();
        updateProgress(10, 'Processing URL...');
        
        const response = await fetch('/api/analyze-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url: url })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            currentTaskId = result.task_id;
            startStatusCheck();
        } else {
            showError(result.error || 'Analysis failed');
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// Show progress section
function showProgress() {
    document.getElementById('progress-section').style.display = 'block';
    document.getElementById('results-section').style.display = 'none';
    updateProgress(0, 'Starting analysis...');
}

// Update progress
function updateProgress(percentage, message) {
    document.getElementById('progress-fill').style.width = percentage + '%';
    document.getElementById('progress-text').textContent = percentage + '%';
    document.getElementById('progress-message').textContent = message;
}

// Start status checking
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkStatus, 2000); // Check every 2 seconds
    checkStatus(); // Check immediately
}

// Check processing status
async function checkStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/status/${currentTaskId}`);
        const status = await response.json();
        
        if (response.ok) {
            updateProgress(status.progress, status.message);
            
            if (status.status === 'completed') {
                clearInterval(statusCheckInterval);
                showResults(status.results);
            } else if (status.status === 'error') {
                clearInterval(statusCheckInterval);
                showError(status.message);
            }
        } else {
            clearInterval(statusCheckInterval);
            showError('Failed to check status');
        }
    } catch (error) {
        clearInterval(statusCheckInterval);
        showError('Network error: ' + error.message);
    }
}

// Show results with enhanced tabs
async function showResults(results) {
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('results-section').style.display = 'block';

    // Display overview content
    displayOverview(results);

    // Load additional content for other tabs
    await loadVisualizations(currentTaskId);
    await loadDatasets(currentTaskId);
    await loadModels(currentTaskId);
}

// Display overview tab content
function displayOverview(results) {
    const overviewContent = document.getElementById('overview-content');
    overviewContent.innerHTML = '';

    // Document metadata
    if (results.metadata) {
        const metadataCard = createCard('Document Information', [
            `<strong>Title:</strong> ${results.metadata.title || 'N/A'}`,
            `<strong>Author:</strong> ${results.metadata.author || 'N/A'}`,
            `<strong>Pages:</strong> ${results.metadata.pages || 'N/A'}`,
            `<strong>Creation Date:</strong> ${results.metadata.creation_date || 'N/A'}`
        ]);
        overviewContent.appendChild(metadataCard);
    }

    // Content statistics
    const stats = {
        'Text Length': results.text ? results.text.length.toLocaleString() + ' characters' : 'N/A',
        'Images': results.images ? results.images.length : 0,
        'Tables': results.tables ? results.tables.length : 0,
        'Keywords': results.insights?.tfidf_keywords ? results.insights.tfidf_keywords.length : 0,
        'Named Entities': results.insights?.named_entities ? results.insights.named_entities.length : 0
    };

    const statsItems = Object.entries(stats).map(([key, value]) =>
        `<div class="stat-item"><span class="stat-label">${key}</span><span class="stat-value">${value}</span></div>`
    );

    const statsCard = createCard('Content Statistics', statsItems);
    overviewContent.appendChild(statsCard);

    // Top keywords
    if (results.insights?.tfidf_keywords) {
        const keywords = results.insights.tfidf_keywords.slice(0, 10);
        const keywordTags = keywords.map(([word, score]) =>
            `<span class="keyword-tag" title="Score: ${score.toFixed(3)}">${word}</span>`
        ).join('');

        const keywordsCard = createCard('Top Keywords', [`<div class="keywords-list">${keywordTags}</div>`]);
        overviewContent.appendChild(keywordsCard);
    }

    // Named entities
    if (results.insights?.named_entities) {
        const entities = results.insights.named_entities.slice(0, 15);
        const entityTags = entities.map(entity =>
            `<span class="entity-tag" title="${entity.label}">${entity.text}</span>`
        ).join('');

        const entitiesCard = createCard('Named Entities', [`<div class="entities-list">${entityTags}</div>`]);
        overviewContent.appendChild(entitiesCard);
    }
}

// Helper function to create cards
function createCard(title, items) {
    const card = document.createElement('div');
    card.className = 'result-card';
    card.innerHTML = `
        <h3><i class="fas fa-info-circle"></i> ${title}</h3>
        ${items.join('')}
    `;
    return card;
}

// Load visualizations
async function loadVisualizations(taskId) {
    const visualizationsContent = document.getElementById('visualizations-content');
    visualizationsContent.innerHTML = '<div class="loading-card"><div class="loading-spinner"></div><p>Generating visualizations...</p></div>';

    try {
        const response = await fetch(`/api/visualizations/${taskId}`);
        const data = await response.json();

        visualizationsContent.innerHTML = '';

        if (response.ok) {
            // Display each visualization
            Object.values(data).forEach(viz => {
                if (viz.error) {
                    console.error('Visualization error:', viz.error);
                    return;
                }

                const vizCard = document.createElement('div');
                vizCard.className = 'visualization-card';
                vizCard.innerHTML = `
                    <h3><i class="fas fa-chart-line"></i> ${viz.title}</h3>
                    <p>${viz.description}</p>
                    <div class="chart-container">
                        <img src="data:image/png;base64,${viz.data}" alt="${viz.title}">
                    </div>
                `;
                visualizationsContent.appendChild(vizCard);
            });
        } else {
            throw new Error(data.error || 'Failed to load visualizations');
        }
    } catch (error) {
        visualizationsContent.innerHTML = `<div class="loading-card"><p>Error loading visualizations: ${error.message}</p></div>`;
    }
}

// Load datasets
async function loadDatasets(taskId) {
    const datasetsContent = document.getElementById('datasets-content');
    datasetsContent.innerHTML = '<div class="loading-card"><div class="loading-spinner"></div><p>Extracting dataset information...</p></div>';

    try {
        const response = await fetch(`/api/datasets/${taskId}`);
        const data = await response.json();

        datasetsContent.innerHTML = '';

        if (response.ok) {
            // Found links
            if (data.found_links && data.found_links.length > 0) {
                const linksCard = document.createElement('div');
                linksCard.className = 'result-card';
                linksCard.innerHTML = `
                    <h3><i class="fas fa-link"></i> Found Dataset Links</h3>
                    ${data.found_links.map(link => `
                        <a href="${link.url}" target="_blank" class="dataset-link">
                            <div class="link-title">${link.url}</div>
                            <div class="link-description">${link.description}</div>
                        </a>
                    `).join('')}
                `;
                datasetsContent.appendChild(linksCard);
            }

            // Mentioned datasets
            if (data.mentioned_datasets && data.mentioned_datasets.length > 0) {
                const mentionedCard = document.createElement('div');
                mentionedCard.className = 'result-card';
                mentionedCard.innerHTML = `
                    <h3><i class="fas fa-tags"></i> Mentioned Datasets</h3>
                    ${data.mentioned_datasets.map(dataset => `
                        <a href="${dataset.search_url}" target="_blank" class="dataset-link">
                            <div class="link-title">${dataset.name}</div>
                            <div class="link-description">${dataset.context}</div>
                        </a>
                    `).join('')}
                `;
                datasetsContent.appendChild(mentionedCard);
            }

            // Suggested repositories
            if (data.suggested_repositories) {
                const suggestedCard = document.createElement('div');
                suggestedCard.className = 'result-card';
                suggestedCard.innerHTML = `
                    <h3><i class="fas fa-search"></i> Suggested Dataset Repositories</h3>
                    ${data.suggested_repositories.map(repo => `
                        <a href="${repo.url}" target="_blank" class="dataset-link">
                            <div class="link-title">${repo.name}</div>
                            <div class="link-description">${repo.description}</div>
                        </a>
                    `).join('')}
                `;
                datasetsContent.appendChild(suggestedCard);
            }

            // Data availability statement
            if (data.data_availability) {
                const availabilityCard = document.createElement('div');
                availabilityCard.className = 'result-card';
                availabilityCard.innerHTML = `
                    <h3><i class="fas fa-info-circle"></i> Data Availability Statement</h3>
                    <p>${data.data_availability}</p>
                `;
                datasetsContent.appendChild(availabilityCard);
            }

            if (!data.found_links?.length && !data.mentioned_datasets?.length) {
                datasetsContent.innerHTML = '<div class="loading-card"><p>No specific dataset links found in the paper. Check the suggested repositories above.</p></div>';
            }
        } else {
            throw new Error(data.error || 'Failed to load datasets');
        }
    } catch (error) {
        datasetsContent.innerHTML = `<div class="loading-card"><p>Error loading datasets: ${error.message}</p></div>`;
    }
}

// Load AI models
async function loadModels(taskId) {
    const modelsContent = document.getElementById('models-content');
    modelsContent.innerHTML = '<div class="loading-card"><div class="loading-spinner"></div><p>Generating AI model code with Groq...</p></div>';

    try {
        const response = await fetch(`/api/generate-models/${taskId}`);
        const data = await response.json();

        modelsContent.innerHTML = '';

        if (response.ok) {
            // Analysis summary
            if (data.analysis_summary) {
                const summaryCard = document.createElement('div');
                summaryCard.className = 'result-card';
                summaryCard.innerHTML = `
                    <h3><i class="fas fa-brain"></i> AI Analysis Summary</h3>
                    <p>${data.analysis_summary}</p>
                `;
                modelsContent.appendChild(summaryCard);
            }

            // Generated models
            if (data.generated_models && data.generated_models.length > 0) {
                data.generated_models.forEach(model => {
                    const modelCard = document.createElement('div');
                    modelCard.className = 'model-card';
                    modelCard.innerHTML = `
                        <div class="model-header">
                            <h3 class="model-title">${model.name}</h3>
                            <span class="framework-badge">${model.framework}</span>
                        </div>
                        <p>${model.description}</p>
                        <div class="code-container">
                            <pre><code>${model.code}</code></pre>
                        </div>
                        <button class="copy-button" onclick="copyToClipboard('${model.name}', \`${model.code.replace(/`/g, '\\`')}\`)">
                            <i class="fas fa-copy"></i> Copy Code
                        </button>
                        ${model.requirements ? `
                            <div style="margin-top: 1rem;">
                                <strong>Requirements:</strong> ${model.requirements.join(', ')}
                            </div>
                        ` : ''}
                    `;
                    modelsContent.appendChild(modelCard);
                });
            }

            // Recommendations
            if (data.recommendations && data.recommendations.length > 0) {
                const recommendationsCard = document.createElement('div');
                recommendationsCard.className = 'result-card';
                recommendationsCard.innerHTML = `
                    <h3><i class="fas fa-lightbulb"></i> Recommendations</h3>
                    <ul>
                        ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                `;
                modelsContent.appendChild(recommendationsCard);
            }

            if (!data.generated_models?.length && !data.analysis_summary) {
                modelsContent.innerHTML = '<div class="loading-card"><p>No models could be generated from this paper.</p></div>';
            }
        } else {
            throw new Error(data.error || 'Failed to generate models');
        }
    } catch (error) {
        modelsContent.innerHTML = `<div class="loading-card"><p>Error generating models: ${error.message}</p></div>`;
    }
}

// Copy code to clipboard
function copyToClipboard(modelName, code) {
    navigator.clipboard.writeText(code).then(() => {
        // Show success feedback
        const button = event.target.closest('.copy-button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.style.background = 'var(--success)';

        setTimeout(() => {
            button.innerHTML = originalText;
            button.style.background = 'var(--navy-blue)';
        }, 2000);
    }).catch(err => {
        alert('Failed to copy code to clipboard');
    });
}

// Utility functions
function formatNumber(num) {
    if (num === null || num === undefined) return 'N/A';
    return num.toLocaleString();
}

function formatScore(score) {
    if (score === null || score === undefined) return 'N/A';
    return score.toFixed(1);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleDateString();
    } catch {
        return dateString;
    }
}

// Show error message
function showError(message) {
    document.getElementById('progress-section').style.display = 'none';
    
    const errorHTML = `
        <div class="result-card" style="border-left-color: var(--error);">
            <h3 style="color: var(--error);"><i class="fas fa-exclamation-triangle"></i> Error</h3>
            <p style="color: var(--text-dark); margin-top: 1rem;">${message}</p>
            <button class="analyze-btn" style="margin-top: 1rem;" onclick="location.reload()">
                <i class="fas fa-refresh"></i> Try Again
            </button>
        </div>
    `;
    
    document.getElementById('results-section').innerHTML = errorHTML;
    document.getElementById('results-section').style.display = 'block';
}
