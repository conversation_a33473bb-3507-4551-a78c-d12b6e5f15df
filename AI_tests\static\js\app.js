// SciML Platform - Frontend JavaScript

let currentTaskId = null;
let statusCheckInterval = null;

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// File upload handling
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        showError('Please select a PDF file.');
        return;
    }
    
    if (file.size > 50 * 1024 * 1024) { // 50MB
        showError('File size must be less than 50MB.');
        return;
    }
    
    uploadFile(file);
}

// Drag and drop handling
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.upload-area');
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.name.toLowerCase().endsWith('.pdf')) {
                uploadFile(file);
            } else {
                showError('Please drop a PDF file.');
            }
        }
    });
});

// Upload file to server
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showProgress();
        updateProgress(10, 'Uploading file...');
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            currentTaskId = result.task_id;
            startStatusCheck();
        } else {
            showError(result.error || 'Upload failed');
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// URL analysis handling
async function handleUrlAnalysis() {
    const urlInput = document.getElementById('urlInput');
    const url = urlInput.value.trim();
    
    if (!url) {
        showError('Please enter a URL.');
        return;
    }
    
    try {
        showProgress();
        updateProgress(10, 'Processing URL...');
        
        const response = await fetch('/api/analyze-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url: url })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            currentTaskId = result.task_id;
            startStatusCheck();
        } else {
            showError(result.error || 'Analysis failed');
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// Show progress section
function showProgress() {
    document.getElementById('progress-section').style.display = 'block';
    document.getElementById('results-section').style.display = 'none';
    updateProgress(0, 'Starting analysis...');
}

// Update progress
function updateProgress(percentage, message) {
    document.getElementById('progress-fill').style.width = percentage + '%';
    document.getElementById('progress-text').textContent = percentage + '%';
    document.getElementById('progress-message').textContent = message;
}

// Start status checking
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkStatus, 2000); // Check every 2 seconds
    checkStatus(); // Check immediately
}

// Check processing status
async function checkStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/status/${currentTaskId}`);
        const status = await response.json();
        
        if (response.ok) {
            updateProgress(status.progress, status.message);
            
            if (status.status === 'completed') {
                clearInterval(statusCheckInterval);
                showResults(status.results);
            } else if (status.status === 'error') {
                clearInterval(statusCheckInterval);
                showError(status.message);
            }
        } else {
            clearInterval(statusCheckInterval);
            showError('Failed to check status');
        }
    } catch (error) {
        clearInterval(statusCheckInterval);
        showError('Network error: ' + error.message);
    }
}

// Show results
function showResults(results) {
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('results-section').style.display = 'block';
    
    const resultsContainer = document.getElementById('results-section');
    resultsContainer.innerHTML = generateResultsHTML(results);
}

// Generate results HTML
function generateResultsHTML(results) {
    const metadata = results.metadata || {};
    const insights = results.insights || {};
    const statistics = results.statistics || {};
    
    return `
        <div class="results-grid">
            <!-- Document Info -->
            <div class="result-card">
                <h3><i class="fas fa-file-pdf"></i> Document Information</h3>
                <div class="stat-item">
                    <span class="stat-label">Title</span>
                    <span class="stat-value">${metadata.title || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Author</span>
                    <span class="stat-value">${metadata.author || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Pages</span>
                    <span class="stat-value">${metadata.pages || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Created</span>
                    <span class="stat-value">${formatDate(metadata.created) || 'N/A'}</span>
                </div>
            </div>

            <!-- Text Statistics -->
            <div class="result-card">
                <h3><i class="fas fa-chart-bar"></i> Text Analysis</h3>
                <div class="stat-item">
                    <span class="stat-label">Total Words</span>
                    <span class="stat-value">${formatNumber(insights.statistics?.total_words) || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Unique Words</span>
                    <span class="stat-value">${formatNumber(insights.statistics?.unique_words) || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Sentences</span>
                    <span class="stat-value">${formatNumber(insights.statistics?.sentences) || 'N/A'}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Readability Score</span>
                    <span class="stat-value">${formatScore(insights.statistics?.readability_score) || 'N/A'}</span>
                </div>
            </div>

            <!-- Visual Content -->
            <div class="result-card">
                <h3><i class="fas fa-images"></i> Visual Content</h3>
                <div class="stat-item">
                    <span class="stat-label">Images</span>
                    <span class="stat-value">${statistics.total_images || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Tables</span>
                    <span class="stat-value">${statistics.total_tables || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Equations</span>
                    <span class="stat-value">${statistics.total_equations || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Sections</span>
                    <span class="stat-value">${statistics.sections_found || 0}</span>
                </div>
            </div>

            <!-- Processing Info -->
            <div class="result-card">
                <h3><i class="fas fa-cog"></i> Processing Details</h3>
                <div class="stat-item">
                    <span class="stat-label">Text Length</span>
                    <span class="stat-value">${formatNumber(statistics.text_length)} chars</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Visual Elements</span>
                    <span class="stat-value">${(statistics.total_images || 0) + (statistics.total_tables || 0)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Processed At</span>
                    <span class="stat-value">${formatDate(results.processing_timestamp)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Status</span>
                    <span class="stat-value" style="color: var(--success);">✓ Complete</span>
                </div>
            </div>
        </div>

        <!-- Keywords Section -->
        <div class="result-card" style="margin-top: 1.5rem;">
            <h3><i class="fas fa-tags"></i> Top Keywords</h3>
            <div class="keywords-list">
                ${generateKeywordTags(insights.tfidf_keywords)}
            </div>
        </div>

        <!-- Named Entities Section -->
        <div class="result-card" style="margin-top: 1.5rem;">
            <h3><i class="fas fa-user-tag"></i> Named Entities</h3>
            <div class="entities-list">
                ${generateEntityTags(insights.named_entities)}
            </div>
        </div>

        <!-- Scientific Terms Section -->
        <div class="result-card" style="margin-top: 1.5rem;">
            <h3><i class="fas fa-flask"></i> Scientific Terms</h3>
            ${generateScientificTerms(insights.scientific_terms)}
        </div>
    `;
}

// Generate keyword tags
function generateKeywordTags(keywords) {
    if (!keywords || !Array.isArray(keywords)) return '<span class="stat-value">No keywords found</span>';
    
    return keywords.slice(0, 15).map(([keyword, score]) => 
        `<span class="keyword-tag">${keyword} (${score.toFixed(3)})</span>`
    ).join('');
}

// Generate entity tags
function generateEntityTags(entities) {
    if (!entities || !Array.isArray(entities)) return '<span class="stat-value">No entities found</span>';
    
    return entities.slice(0, 15).map(entity => 
        `<span class="entity-tag">${entity.text} (${entity.label})</span>`
    ).join('');
}

// Generate scientific terms
function generateScientificTerms(terms) {
    if (!terms || typeof terms !== 'object') return '<span class="stat-value">No scientific terms found</span>';
    
    let html = '';
    for (const [category, termList] of Object.entries(terms)) {
        if (termList && termList.length > 0) {
            html += `
                <div class="stat-item">
                    <span class="stat-label">${category.toUpperCase()}</span>
                    <span class="stat-value">${termList.slice(0, 3).join(', ')}</span>
                </div>
            `;
        }
    }
    return html || '<span class="stat-value">No scientific terms found</span>';
}

// Utility functions
function formatNumber(num) {
    if (num === null || num === undefined) return 'N/A';
    return num.toLocaleString();
}

function formatScore(score) {
    if (score === null || score === undefined) return 'N/A';
    return score.toFixed(1);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleDateString();
    } catch {
        return dateString;
    }
}

// Show error message
function showError(message) {
    document.getElementById('progress-section').style.display = 'none';
    
    const errorHTML = `
        <div class="result-card" style="border-left-color: var(--error);">
            <h3 style="color: var(--error);"><i class="fas fa-exclamation-triangle"></i> Error</h3>
            <p style="color: var(--text-dark); margin-top: 1rem;">${message}</p>
            <button class="analyze-btn" style="margin-top: 1rem;" onclick="location.reload()">
                <i class="fas fa-refresh"></i> Try Again
            </button>
        </div>
    `;
    
    document.getElementById('results-section').innerHTML = errorHTML;
    document.getElementById('results-section').style.display = 'block';
}
