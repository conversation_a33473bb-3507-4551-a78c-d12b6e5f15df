import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/UploadPaper.css';

const UploadPaper = () => {
  const [dragActive, setDragActive] = useState(false);
  const [file, setFile] = useState(null);
  const [paperUrl, setPaperUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [activeTab, setActiveTab] = useState('upload');
  const fileInputRef = useRef(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  // Handle file input change
  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  // Handle file selection
  const handleFile = (file) => {
    // Check if file is PDF
    if (file.type !== 'application/pdf') {
      setUploadStatus({
        success: false,
        message: 'Please upload a PDF file'
      });
      return;
    }

    // Check file size (limit to 20MB)
    if (file.size > 20 * 1024 * 1024) {
      setUploadStatus({
        success: false,
        message: 'File size exceeds 20MB limit'
      });
      return;
    }

    setFile(file);
    setUploadStatus(null);
  };

  // Handle button click to open file dialog
  const onButtonClick = () => {
    fileInputRef.current.click();
  };

  // Handle URL input change
  const handleUrlChange = (e) => {
    setPaperUrl(e.target.value);
    setUploadStatus(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (activeTab === 'upload' && !file) {
      setUploadStatus({
        success: false,
        message: 'Please select a file to upload'
      });
      return;
    }

    if (activeTab === 'url' && !paperUrl) {
      setUploadStatus({
        success: false,
        message: 'Please enter a valid URL'
      });
      return;
    }

    setIsLoading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return prev + 5;
        });
      }, 300);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));

      clearInterval(progressInterval);
      setUploadProgress(100);

      setUploadStatus({
        success: true,
        message: activeTab === 'upload'
          ? `Successfully uploaded ${file.name}`
          : 'Successfully processed paper from URL'
      });

      // Reset form after successful upload
      if (activeTab === 'upload') {
        setFile(null);
      } else {
        setPaperUrl('');
      }

    } catch (error) {
      setUploadStatus({
        success: false,
        message: 'An error occurred during upload. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center pt-24 lg:pt-20 px-4 bg-white">
        {/* Decorative elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-highlight opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-primary opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <motion.div
          className="w-full max-w-3xl relative z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            className="text-center mb-6 lg:mb-8"
            variants={itemVariants}
          >
            <h1 className="text-3xl sm:text-4xl font-bold text-accent">
              Upload Your <span className="text-highlight">Research Paper</span>
            </h1>
            <p className="mt-3 lg:mt-4 text-base lg:text-lg text-primary max-w-2xl mx-auto">
              Transform your scientific paper into interactive tools, APIs, and models using our advanced AI technology.
            </p>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl border border-gray-100"
            variants={itemVariants}
          >
            {/* Tab navigation */}
            <div className="flex mb-4 lg:mb-6 border-b border-gray-200">
              <button
                className={`py-2 px-3 lg:px-4 font-medium text-sm focus:outline-none ${
                  activeTab === 'upload'
                    ? 'text-highlight border-b-2 border-highlight'
                    : 'text-accent hover:text-highlight'
                }`}
                onClick={() => setActiveTab('upload')}
              >
                Upload PDF
              </button>
              <button
                className={`py-2 px-3 lg:px-4 font-medium text-sm focus:outline-none ${
                  activeTab === 'url'
                    ? 'text-highlight border-b-2 border-highlight'
                    : 'text-accent hover:text-highlight'
                }`}
                onClick={() => setActiveTab('url')}
              >
                Paper URL
              </button>
            </div>

            <form onSubmit={handleSubmit}>
              {activeTab === 'upload' ? (
                <div
                  className={`upload-container relative border-2 border-dashed rounded-lg p-4 sm:p-6 lg:p-8 text-center ${
                    dragActive ? 'border-highlight bg-highlight bg-opacity-5' : 'border-gray-300 bg-gray-50'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept=".pdf"
                    onChange={handleChange}
                  />

                  {file ? (
                    <div className="py-4">
                      <div className="flex items-center justify-center mb-4">
                        <svg className="w-12 h-12 text-highlight" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-lg font-medium text-accent">{file.name}</p>
                      <p className="text-sm text-primary mt-1">{(file.size / (1024 * 1024)).toFixed(2)} MB</p>

                      <button
                        type="button"
                        className="mt-4 text-sm text-highlight hover:text-accent transition-colors duration-200"
                        onClick={() => setFile(null)}
                      >
                        Change file
                      </button>
                    </div>
                  ) : (
                    <>
                      <svg className="mx-auto h-12 w-12 lg:h-16 lg:w-16 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="mt-3 lg:mt-4 text-base lg:text-lg font-medium text-accent">
                        Drag & drop your PDF here
                      </p>
                      <p className="mt-2 text-sm text-primary">
                        or <button type="button" className="text-highlight hover:text-accent transition-colors duration-200" onClick={onButtonClick}>browse files</button>
                      </p>
                      <p className="mt-1 text-xs text-primary">
                        Maximum file size: 20MB
                      </p>
                    </>
                  )}
                </div>
              ) : (
                <div className="py-4">
                  <label className="block text-sm font-medium text-accent mb-2">
                    Paper URL
                  </label>
                  <input
                    type="url"
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-highlight focus:border-transparent text-primary"
                    placeholder="https://arxiv.org/pdf/2106.12423.pdf"
                    value={paperUrl}
                    onChange={handleUrlChange}
                  />
                  <p className="mt-2 text-xs text-primary">
                    Enter the URL of a publicly accessible research paper (PDF, arXiv, DOI, etc.)
                  </p>
                </div>
              )}

              {uploadStatus && (
                <div className={`mt-4 p-3 rounded-md ${
                  uploadStatus.success ? 'bg-green-500 bg-opacity-20 text-green-700' : 'bg-red-500 bg-opacity-20 text-red-700'
                }`}>
                  {uploadStatus.message}
                </div>
              )}

              {isLoading && (
                <div className="mt-6">
                  <div className="relative pt-1">
                    <div className="flex mb-2 items-center justify-between">
                      <div>
                        <span className="text-xs font-semibold inline-block text-primary">
                          {uploadProgress}% Complete
                        </span>
                      </div>
                    </div>
                    <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                      <div
                        style={{ width: `${uploadProgress}%` }}
                        className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-highlight to-accent"
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-4 lg:mt-6">
                <motion.button
                  type="submit"
                  className="w-full py-3 px-4 bg-highlight text-white rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-highlight focus:ring-opacity-50 shadow-lg hover:shadow-xl transition-all duration-300 text-sm lg:text-base"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={isLoading}
                >
                  {isLoading ? 'Processing...' : 'Upload Paper'}
                </motion.button>
              </div>
            </form>
          </motion.div>

          <motion.div
            className="mt-6 lg:mt-8 text-center text-xs lg:text-sm text-accent"
            variants={itemVariants}
          >
            By uploading a paper, you agree to our <a href="#terms" className="text-highlight hover:text-accent">Terms of Service</a> and <a href="#privacy" className="text-highlight hover:text-accent">Privacy Policy</a>.
          </motion.div>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
};

export default UploadPaper;
