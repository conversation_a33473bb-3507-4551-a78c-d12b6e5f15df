#!/usr/bin/env python3
"""
SciML Backend API Test Script
Test the backend API endpoints
"""

import requests
import json
import time
import sys

API_BASE_URL = 'http://localhost:5000'

def test_health_check():
    """Test health check endpoint"""
    try:
        response = requests.get(f'{API_BASE_URL}/health')
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Is the server running?")
        return False

def test_api_info():
    """Test API info endpoint"""
    try:
        response = requests.get(f'{API_BASE_URL}/')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Info: {data.get('message', 'Unknown')}")
            return True
        else:
            print(f"❌ API info failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API info error: {e}")
        return False

def test_url_analysis():
    """Test URL analysis with ArXiv paper"""
    try:
        # Test with a sample ArXiv paper
        test_url = "https://arxiv.org/abs/2301.07041"  # A real ArXiv paper
        
        print(f"🔄 Testing URL analysis with: {test_url}")
        
        response = requests.post(
            f'{API_BASE_URL}/api/analyze-url',
            json={'url': test_url},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('task_id')
            print(f"✅ URL analysis started. Task ID: {task_id}")
            
            # Monitor progress for a short time
            for i in range(5):
                time.sleep(2)
                status_response = requests.get(f'{API_BASE_URL}/api/status/{task_id}')
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get('status', 'unknown')
                    progress = status_data.get('progress', 0)
                    message = status_data.get('message', '')
                    
                    print(f"   Status: {status} ({progress}%) - {message}")
                    
                    if status == 'completed':
                        print("✅ URL analysis completed successfully!")
                        return True
                    elif status == 'error':
                        print(f"❌ URL analysis failed: {message}")
                        return False
                else:
                    print(f"❌ Status check failed: {status_response.status_code}")
                    return False
            
            print("⏱️  Analysis still in progress (this is normal for large documents)")
            return True
            
        else:
            print(f"❌ URL analysis failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ URL analysis error: {e}")
        return False

def test_debug_tasks():
    """Test debug tasks endpoint"""
    try:
        response = requests.get(f'{API_BASE_URL}/api/debug/tasks')
        if response.status_code == 200:
            data = response.json()
            total_tasks = data.get('total_tasks', 0)
            print(f"✅ Debug tasks: {total_tasks} tasks found")
            
            # Show recent tasks
            tasks = data.get('tasks', [])
            if tasks:
                print("   Recent tasks:")
                for task in tasks[-3:]:  # Show last 3 tasks
                    task_id = task.get('task_id', 'unknown')[:8]
                    status = task.get('status', 'unknown')
                    print(f"     - {task_id}... : {status}")
            
            return True
        else:
            print(f"❌ Debug tasks failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Debug tasks error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 SciML Backend API Tests")
    print("=" * 40)
    
    tests = [
        ("Health Check", test_health_check),
        ("API Info", test_api_info),
        ("Debug Tasks", test_debug_tasks),
        ("URL Analysis", test_url_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   Test failed: {test_name}")
        except Exception as e:
            print(f"   Test error: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the backend configuration.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
