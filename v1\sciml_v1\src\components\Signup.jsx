import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/Auth.css';

const Signup = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: { duration: 0.4 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });

    // Clear any previous errors when user starts typing again
    setError('');
  };

  // Check password strength
  useEffect(() => {
    const { password } = formData;
    if (!password) {
      setPasswordStrength(0);
      return;
    }

    let strength = 0;
    // Length check
    if (password.length >= 8) strength += 1;
    // Contains lowercase
    if (/[a-z]/.test(password)) strength += 1;
    // Contains uppercase
    if (/[A-Z]/.test(password)) strength += 1;
    // Contains number
    if (/[0-9]/.test(password)) strength += 1;
    // Contains special character
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    setPasswordStrength(strength);
  }, [formData.password]);

  // Get password strength class
  const getPasswordStrengthClass = () => {
    if (passwordStrength === 0) return '';
    if (passwordStrength <= 2) return 'strength-weak';
    if (passwordStrength <= 3) return 'strength-medium';
    if (passwordStrength === 4) return 'strength-strong';
    return 'strength-very-strong';
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.fullName || !formData.email || !formData.password || !formData.confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (passwordStrength < 3) {
      setError('Please use a stronger password');
      return;
    }

    if (!formData.agreeToTerms) {
      setError('You must agree to the terms and conditions');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Here you would normally make an API call to register the user
      console.log('Registration attempt with:', formData);

      // Show success message
      setSuccessMessage('Account created successfully! Redirecting to login...');

      // Redirect to login after a delay
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);

    } catch (err) {
      setError('Registration failed. Please try again.');
      console.error('Registration error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center py-20 px-4 bg-gradient-to-b from-accent to-primary">
        {/* Decorative elements */}
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-highlight opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-primary opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <motion.div
          className="w-full max-w-md relative z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <div className="relative z-10 auth-container rounded-xl p-8 shadow-xl">
            <motion.div className="text-center mb-8" variants={itemVariants}>
              <h2 className="text-3xl font-bold text-white">Create Account</h2>
              <p className="mt-2 text-gray-300">Join the SciML platform</p>
            </motion.div>

            {error && (
              <motion.div
                className="mb-6 p-3 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-md text-white text-sm"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {error}
              </motion.div>
            )}

            {successMessage && (
              <motion.div
                className="mb-6 p-3 bg-green-500 bg-opacity-20 border border-green-500 border-opacity-30 rounded-md text-white text-sm"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {successMessage}
              </motion.div>
            )}

            <form onSubmit={handleSubmit}>
              <motion.div className="mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    className="auth-input w-full px-4 py-3 rounded-lg focus:outline-none"
                    placeholder=" "
                    value={formData.fullName}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="fullName" className="floating-label">Full Name</label>
                </div>
              </motion.div>

              <motion.div className="mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="auth-input w-full px-4 py-3 rounded-lg focus:outline-none"
                    placeholder=" "
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="email" className="floating-label">Email Address</label>
                </div>
              </motion.div>

              <motion.div className="mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="password"
                    id="password"
                    name="password"
                    className="auth-input w-full px-4 py-3 rounded-lg focus:outline-none"
                    placeholder=" "
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="password" className="floating-label">Password</label>

                  {formData.password && (
                    <div className={`password-strength ${getPasswordStrengthClass()}`}></div>
                  )}

                  {formData.password && (
                    <p className="mt-1 text-xs text-gray-300">
                      {passwordStrength <= 2 && "Weak - Add uppercase, numbers, or special characters"}
                      {passwordStrength === 3 && "Medium - Good password"}
                      {passwordStrength === 4 && "Strong - Great password"}
                      {passwordStrength >= 5 && "Very Strong - Excellent password"}
                    </p>
                  )}
                </div>
              </motion.div>

              <motion.div className="mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    className="auth-input w-full px-4 py-3 rounded-lg focus:outline-none"
                    placeholder=" "
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="confirmPassword" className="floating-label">Confirm Password</label>
                </div>
              </motion.div>

              <motion.div className="mb-6" variants={itemVariants}>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="agreeToTerms"
                    name="agreeToTerms"
                    className="h-4 w-4 bg-transparent border-2 border-gray-400 rounded focus:ring-highlight focus:ring-offset-0"
                    checked={formData.agreeToTerms}
                    onChange={handleChange}
                    required
                  />
                  <label htmlFor="agreeToTerms" className="ml-2 text-sm text-gray-300">
                    I agree to the <a href="#terms" className="text-highlight hover:text-white">Terms of Service</a> and <a href="#privacy" className="text-highlight hover:text-white">Privacy Policy</a>
                  </label>
                </div>
              </motion.div>

              <motion.div variants={itemVariants}>
                <button
                  type="submit"
                  className="auth-button w-full py-3 px-4 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-highlight focus:ring-opacity-50"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating account...
                    </span>
                  ) : (
                    'Create Account'
                  )}
                </button>
              </motion.div>
            </form>

            <motion.div className="mt-8 text-center" variants={itemVariants}>
              <p className="text-gray-300">
                Already have an account?{' '}
                <Link
                  to="/login"
                  className="text-highlight hover:text-white transition-colors duration-200 font-medium"
                >
                  Sign in
                </Link>
              </p>
            </motion.div>
          </div>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
};

export default Signup;
