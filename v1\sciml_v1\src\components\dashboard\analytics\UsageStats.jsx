import { useState } from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../../DashboardLayout';

const UsageStats = () => {
  const [timeRange, setTimeRange] = useState('week');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Mock data for different time ranges
  const mockData = {
    week: {
      apiCalls: 1250,
      paperAnalysis: 45,
      modelTraining: 12,
      storageUsed: '28.5 GB',
      usageTrend: [65, 75, 85, 70, 90, 85, 95],
      resourceUtilization: [
        { name: 'CPU', value: 75 },
        { name: 'Memory', value: 60 },
        { name: 'Storage', value: 45 },
        { name: 'GPU', value: 85 }
      ]
    },
    month: {
      apiCalls: 5200,
      paperAnalysis: 180,
      modelTraining: 45,
      storageUsed: '45.2 GB',
      usageTrend: [65, 70, 75, 80, 85, 90, 95, 85, 80, 75, 70, 65, 70, 75, 80, 85, 90, 85, 80, 75, 70, 65, 70, 75, 80, 85, 90, 85, 80, 75],
      resourceUtilization: [
        { name: 'CPU', value: 65 },
        { name: 'Memory', value: 55 },
        { name: 'Storage', value: 40 },
        { name: 'GPU', value: 75 }
      ]
    },
    year: {
      apiCalls: 62400,
      paperAnalysis: 2160,
      modelTraining: 540,
      storageUsed: '156.8 GB',
      usageTrend: [65, 70, 75, 80, 85, 90, 85, 80, 75, 70, 65, 70],
      resourceUtilization: [
        { name: 'CPU', value: 70 },
        { name: 'Memory', value: 65 },
        { name: 'Storage', value: 55 },
        { name: 'GPU', value: 80 }
      ]
    }
  };

  const currentData = mockData[timeRange];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-2xl lg:text-3xl font-bold text-accent">Usage Statistics</h1>
          <p className="mt-2 text-primary">Monitor your API usage and resource utilization</p>
        </motion.div>

        {/* Time Range Selector */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 mb-8 p-4">
          <div className="flex flex-wrap gap-2">
            {['week', 'month', 'year'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  timeRange === range
                    ? 'bg-highlight text-white'
                    : 'bg-gray-100 text-primary hover:bg-gray-200'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Statistics Overview */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'API Calls',
              value: currentData.apiCalls.toLocaleString(),
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              )
            },
            {
              label: 'Paper Analysis',
              value: currentData.paperAnalysis,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )
            },
            {
              label: 'Model Training',
              value: currentData.modelTraining,
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              )
            },
            {
              label: 'Storage Used',
              value: currentData.storageUsed,
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Usage Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* API Usage Trend */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
            variants={itemVariants}
          >
            <h2 className="text-lg font-semibold text-accent mb-6">API Usage Trend</h2>
            <div className="h-64 flex items-end space-x-2">
              {currentData.usageTrend.map((value, index) => (
                <div
                  key={index}
                  className="flex-1 bg-highlight/20 rounded-t-lg hover:bg-highlight/30 transition-colors duration-200"
                  style={{ height: `${value}%` }}
                >
                  <div className="h-full bg-highlight rounded-t-lg" style={{ height: `${value}%` }}></div>
                </div>
              ))}
            </div>
            <div className="mt-4 flex justify-between text-xs text-primary">
              <span>Start</span>
              <span>End</span>
            </div>
          </motion.div>

          {/* Resource Utilization */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
            variants={itemVariants}
          >
            <h2 className="text-lg font-semibold text-accent mb-6">Resource Utilization</h2>
            <div className="space-y-4">
              {currentData.resourceUtilization.map((resource, index) => (
                <div key={index}>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-primary">{resource.name}</span>
                    <span className="text-accent">{resource.value}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-highlight h-2 rounded-full"
                      style={{ width: `${resource.value}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Recent Activity */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100"
          variants={itemVariants}
        >
          <div className="p-6">
            <h2 className="text-lg font-semibold text-accent mb-6">Recent Activity</h2>
            <div className="space-y-4">
              {[
                { action: 'API Call', details: 'Paper Analysis API', time: '2 minutes ago' },
                { action: 'Model Training', details: 'BERT Model', time: '15 minutes ago' },
                { action: 'Storage Update', details: 'New dataset uploaded', time: '1 hour ago' },
                { action: 'API Call', details: 'Text Classification API', time: '2 hours ago' },
                { action: 'Model Deployment', details: 'GPT-2 Model', time: '3 hours ago' }
              ].map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-accent">{activity.action}</p>
                      <p className="text-sm text-primary">{activity.details}</p>
                    </div>
                  </div>
                  <span className="text-sm text-gray-500">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default UsageStats; 