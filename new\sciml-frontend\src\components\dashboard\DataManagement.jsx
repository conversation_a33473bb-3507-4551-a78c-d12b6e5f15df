import { useState } from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../DashboardLayout';

const DataManagement = () => {
  const [activeTab, setActiveTab] = useState('datasets');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const tabs = [
    { id: 'datasets', name: 'Datasets' },
    { id: 'storage', name: 'Storage' },
    { id: 'backup', name: 'Backup' },
    { id: 'sharing', name: 'Sharing' }
  ];

  const mockDatasets = [
    {
      id: 1,
      name: 'Research Papers Dataset',
      size: '2.4 GB',
      type: 'CSV',
      lastModified: '2024-03-01',
      status: 'Active'
    },
    {
      id: 2,
      name: 'Model Training Data',
      size: '5.8 GB',
      type: 'JSON',
      lastModified: '2024-02-28',
      status: 'Active'
    },
    {
      id: 3,
      name: 'User Analytics',
      size: '1.2 GB',
      type: 'CSV',
      lastModified: '2024-02-27',
      status: 'Archived'
    }
  ];

  const mockStorage = {
    total: '100 GB',
    used: '45.2 GB',
    free: '54.8 GB',
    usageByType: [
      { type: 'Datasets', size: '25.4 GB', percentage: 56 },
      { type: 'Models', size: '12.8 GB', percentage: 28 },
      { type: 'Backups', size: '7 GB', percentage: 16 }
    ]
  };

  const mockBackups = [
    {
      id: 1,
      name: 'Full System Backup',
      date: '2024-03-01',
      size: '15.2 GB',
      status: 'Completed'
    },
    {
      id: 2,
      name: 'Incremental Backup',
      date: '2024-02-28',
      size: '2.4 GB',
      status: 'Completed'
    },
    {
      id: 3,
      name: 'Database Backup',
      date: '2024-02-27',
      size: '5.8 GB',
      status: 'Completed'
    }
  ];

  const mockSharedDatasets = [
    {
      id: 1,
      name: 'Research Papers Dataset',
      sharedWith: 'Research Team',
      accessLevel: 'Read/Write',
      lastAccessed: '2024-03-01'
    },
    {
      id: 2,
      name: 'Model Training Data',
      sharedWith: 'ML Team',
      accessLevel: 'Read Only',
      lastAccessed: '2024-02-28'
    }
  ];

  return (
    <DashboardLayout>
      <div className="pt-16 sm:pt-20 px-4 lg:px-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Data Management</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Manage your datasets, storage, and backups
              </p>
            </div>
          </div>
        </motion.div>

        {/* Tabs */}
        <motion.div
          className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-highlight text-white'
                    : 'bg-gray-100 text-accent hover:bg-gray-200'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Content */}
        <motion.div
          className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {/* Datasets Tab */}
          {activeTab === 'datasets' && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="space-y-6">
                {mockDatasets.map(dataset => (
                  <motion.div
                    key={dataset.id}
                    variants={itemVariants}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-all duration-200"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-accent">{dataset.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {dataset.type} • {dataset.size} • Last modified: {dataset.lastModified}
                        </p>
                      </div>
                      <div className="mt-4 sm:mt-0">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          dataset.status === 'Active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {dataset.status}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Storage Tab */}
          {activeTab === 'storage' && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="space-y-6">
                <motion.div variants={itemVariants}>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                      <h3 className="text-lg font-medium text-accent">Storage Usage</h3>
                      <p className="text-sm text-gray-600 mt-1 sm:mt-0">
                        {mockStorage.used} of {mockStorage.total} used
                      </p>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-highlight h-2.5 rounded-full"
                        style={{ width: `${(parseFloat(mockStorage.used) / parseFloat(mockStorage.total)) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <h3 className="text-lg font-medium text-accent mb-4">Usage by Type</h3>
                  <div className="space-y-4">
                    {mockStorage.usageByType.map((item, index) => (
                      <div key={index} className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-accent">{item.type}</span>
                          <span className="text-sm text-gray-600 ml-2">({item.size})</span>
                        </div>
                        <div className="w-full sm:w-48 bg-gray-200 rounded-full h-2.5 mt-2 sm:mt-0">
                          <div
                            className="bg-highlight h-2.5 rounded-full"
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}

          {/* Backup Tab */}
          {activeTab === 'backup' && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="space-y-6">
                {mockBackups.map(backup => (
                  <motion.div
                    key={backup.id}
                    variants={itemVariants}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-all duration-200"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-accent">{backup.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {backup.size} • Created on {backup.date}
                        </p>
                      </div>
                      <div className="mt-4 sm:mt-0">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                          {backup.status}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Sharing Tab */}
          {activeTab === 'sharing' && (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="space-y-6">
                {mockSharedDatasets.map(dataset => (
                  <motion.div
                    key={dataset.id}
                    variants={itemVariants}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-all duration-200"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-accent">{dataset.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          Shared with {dataset.sharedWith} • {dataset.accessLevel} access
                        </p>
                      </div>
                      <div className="mt-4 sm:mt-0">
                        <span className="text-sm text-gray-600">
                          Last accessed: {dataset.lastAccessed}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default DataManagement; 