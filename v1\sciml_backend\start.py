#!/usr/bin/env python3
"""
SciML Backend Startup Script
Handles initialization and dependency checking
"""

import os
import sys
import subprocess
import importlib.util

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask',
        'flask_cors',
        'fitz',  # PyMuPDF
        'pdfplumber',
        'requests',
        'bs4',  # beautifulsoup4
        'pandas',
        'numpy',
        'cv2',  # opencv-python
        'PIL',  # Pillow
        'spacy',
        'nltk',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_spacy_model():
    """Check if spaCy English model is installed"""
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        print("✅ spaCy English model is available")
        return True
    except OSError:
        print("❌ spaCy English model not found")
        print("💡 Install with: python -m spacy download en_core_web_sm")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = [
        'storage',
        'storage/status',
        'storage/results'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Storage directories created")

def main():
    """Main startup function"""
    print("🚀 SciML Backend Startup")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check spaCy model
    if not check_spacy_model():
        print("⚠️  Warning: spaCy model missing. NLP features may not work properly.")
    
    # Setup directories
    setup_directories()
    
    print("\n🎯 Starting SciML Backend...")
    print("📍 Server will be available at: http://localhost:5000")
    print("🔧 Health check: http://localhost:5000/health")
    print("📚 API docs: See README.md for endpoint documentation")
    print("\n" + "=" * 40)
    
    # Start the Flask app
    try:
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 SciML Backend stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
