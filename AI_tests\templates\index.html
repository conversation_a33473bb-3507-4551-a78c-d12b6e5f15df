<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SciML Platform - Scientific Literature Analysis</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --navy-blue: #003f5c;
            --dark-navy: #2f4b7c;
            --light-navy: #4a6fa5;
            --accent-blue: #00b5ad;
            --light-gray: #f8fafc;
            --white: #ffffff;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --border-light: #e2e8f0;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--light-gray) 0%, #ffffff 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: var(--white);
            box-shadow: 0 2px 10px rgba(0, 63, 92, 0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--navy-blue);
        }

        .logo i {
            color: var(--accent-blue);
            font-size: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: var(--light-gray);
            border-radius: 20px;
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Content */
        .main {
            padding: 3rem 0;
        }

        .hero {
            text-align: center;
            margin-bottom: 3rem;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--navy-blue), var(--dark-navy));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.25rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Upload Section */
        .upload-section {
            background: var(--white);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 63, 92, 0.08);
            margin-bottom: 2rem;
        }

        .upload-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--border-light);
        }

        .tab-button {
            padding: 12px 24px;
            border: none;
            background: none;
            color: var(--text-light);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: var(--navy-blue);
            border-bottom-color: var(--accent-blue);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* File Upload */
        .upload-area {
            border: 2px dashed var(--border-light);
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--accent-blue);
            background: rgba(0, 181, 173, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--accent-blue);
            background: rgba(0, 181, 173, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--accent-blue);
            margin-bottom: 1rem;
        }

        .upload-text {
            font-size: 1.125rem;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            color: var(--text-light);
            font-size: 0.875rem;
        }

        /* URL Input */
        .url-input-group {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .url-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid var(--border-light);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .url-input:focus {
            outline: none;
            border-color: var(--accent-blue);
        }

        .analyze-btn {
            padding: 12px 24px;
            background: var(--navy-blue);
            color: var(--white);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .analyze-btn:hover {
            background: var(--dark-navy);
        }

        .analyze-btn:disabled {
            background: var(--text-light);
            cursor: not-allowed;
        }

        /* Progress Section */
        .progress-section {
            background: var(--white);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 63, 92, 0.08);
            margin-bottom: 2rem;
            display: none;
        }

        .progress-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .progress-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--accent-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
        }

        .progress-info h3 {
            color: var(--navy-blue);
            margin-bottom: 0.25rem;
        }

        .progress-info p {
            color: var(--text-light);
            font-size: 0.875rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-light);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--navy-blue));
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: var(--text-light);
            font-size: 0.875rem;
        }

        /* Results Section */
        .results-section {
            display: none;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .result-card {
            background: var(--white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 63, 92, 0.08);
            border-left: 4px solid var(--accent-blue);
        }

        .result-card h3 {
            color: var(--navy-blue);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-card h3 i {
            color: var(--accent-blue);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-light);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.875rem;
        }

        .stat-value {
            color: var(--text-dark);
            font-weight: 500;
        }

        /* Keywords and Entities */
        .keywords-list, .entities-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .keyword-tag, .entity-tag {
            padding: 4px 12px;
            background: var(--light-gray);
            color: var(--navy-blue);
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .entity-tag {
            background: rgba(0, 181, 173, 0.1);
            color: var(--accent-blue);
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-light);
            border-radius: 50%;
            border-top-color: var(--accent-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .upload-section, .progress-section {
                padding: 1.5rem;
            }

            .url-input-group {
                flex-direction: column;
            }

            .url-input {
                width: 100%;
            }
        }

        /* Hidden file input */
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-microscope"></i>
                    <span>SciML</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>System Online</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Hero Section -->
            <div class="hero">
                <h1>Scientific Literature Analysis</h1>
                <p>Upload your research papers or provide URLs to extract insights, analyze content, and discover key information with advanced AI-powered processing.</p>
            </div>

            <!-- Upload Section -->
            <div class="upload-section">
                <div class="upload-tabs">
                    <button class="tab-button active" onclick="switchTab('upload')">
                        <i class="fas fa-upload"></i> Upload PDF
                    </button>
                    <button class="tab-button" onclick="switchTab('url')">
                        <i class="fas fa-link"></i> Analyze URL
                    </button>
                </div>

                <!-- File Upload Tab -->
                <div id="upload-tab" class="tab-content active">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Click to upload or drag and drop</div>
                        <div class="upload-subtext">PDF files only, max 50MB</div>
                    </div>
                    <input type="file" id="fileInput" accept=".pdf" onchange="handleFileUpload(event)">
                </div>

                <!-- URL Tab -->
                <div id="url-tab" class="tab-content">
                    <div class="url-input-group">
                        <input type="url" class="url-input" id="urlInput" placeholder="Enter ArXiv URL or direct PDF link...">
                        <button class="analyze-btn" onclick="handleUrlAnalysis()">
                            <i class="fas fa-search"></i>
                            Analyze
                        </button>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div id="progress-section" class="progress-section">
                <div class="progress-header">
                    <div class="progress-icon">
                        <i class="fas fa-cog fa-spin"></i>
                    </div>
                    <div class="progress-info">
                        <h3>Processing Document</h3>
                        <p id="progress-message">Initializing analysis...</p>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-text" id="progress-text">0%</div>
            </div>

            <!-- Results Section -->
            <div id="results-section" class="results-section">
                <!-- Results will be populated here -->
            </div>
        </div>
    </main>

    <script src="/static/js/app.js"></script>
</body>
</html>
