import './App.css';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Home from './components/Home';
import Login from './components/Login';
import Signup from './components/Signup';
import UploadPaper from './components/UploadPaper';
import Contact from './components/Contact';
import Insights from './components/Insights';
import Dashboard from './components/Dashboard';
import APIs from './components/APIs';
import Profile from './components/Profile';
import AllPapers from './components/dashboard/AllPapers';
import ProcessingPapers from './components/dashboard/ProcessingPapers';
import CompletedPapers from './components/dashboard/CompletedPapers';
import DashboardUploadPaper from './components/dashboard/UploadPaper';
import AllModels from './components/dashboard/AllModels';
import TrainingModels from './components/dashboard/TrainingModels';
import DeployedModels from './components/dashboard/DeployedModels';
import CreateModel from './components/dashboard/CreateModel';
import APIManagement from './components/dashboard/APIManagement';
import DataManagement from './components/dashboard/DataManagement';
import Settings from './components/dashboard/Settings';
import UsageStats from './components/dashboard/analytics/UsageStats';
import Performance from './components/dashboard/analytics/Performance';
import Reports from './components/dashboard/analytics/Reports';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/upload-paper" element={<UploadPaper />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/insights" element={<Insights />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/dashboard/papers" element={<AllPapers />} />
            <Route path="/dashboard/papers/processing" element={<ProcessingPapers />} />
            <Route path="/dashboard/papers/completed" element={<CompletedPapers />} />
            <Route path="/dashboard/upload" element={<DashboardUploadPaper />} />
            <Route path="/dashboard/models" element={<AllModels />} />
            <Route path="/dashboard/models/training" element={<TrainingModels />} />
            <Route path="/dashboard/models/deployed" element={<DeployedModels />} />
            <Route path="/dashboard/models/create" element={<CreateModel />} />
            <Route path="/dashboard/apis" element={<APIManagement />} />
            <Route path="/dashboard/data" element={<DataManagement />} />
            <Route path="/dashboard/settings" element={<Settings />} />
            <Route path="/dashboard/analytics/usage" element={<UsageStats />} />
            <Route path="/dashboard/analytics/performance" element={<Performance />} />
            <Route path="/dashboard/analytics/reports" element={<Reports />} />
            <Route path="/apis" element={<APIs />} />
            <Route path="/profile" element={<Profile />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
