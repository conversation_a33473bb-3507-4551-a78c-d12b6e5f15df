import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import DashboardLayout from '../DashboardLayout';

// API service for SciML backend
const API_BASE_URL = 'http://localhost:5000';

const apiService = {
  uploadFile: async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/api/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    return response.json();
  },

  analyzeUrl: async (url) => {
    const response = await fetch(`${API_BASE_URL}/api/analyze-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
    });

    if (!response.ok) {
      throw new Error(`URL analysis failed: ${response.statusText}`);
    }

    return response.json();
  },

  getStatus: async (taskId) => {
    const response = await fetch(`${API_BASE_URL}/api/status/${taskId}`);

    if (!response.ok) {
      throw new Error(`Status check failed: ${response.statusText}`);
    }

    return response.json();
  },

  getResults: async (taskId) => {
    const response = await fetch(`${API_BASE_URL}/api/results/${taskId}`);

    if (!response.ok) {
      throw new Error(`Results fetch failed: ${response.statusText}`);
    }

    return response.json();
  }
};

const DashboardUploadPaper = () => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadComplete, setUploadComplete] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [isUrlProcessing, setIsUrlProcessing] = useState(false);

  const onDrop = useCallback((acceptedFiles) => {
    const newFiles = acceptedFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      status: 'pending',
      progress: 0,
      uploadedAt: new Date(),
      taskId: null,
      type: 'file'
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // Process each file with real API
    newFiles.forEach(fileObj => {
      processFile(fileObj.id);
    });
  }, []);

  const processFile = async (fileId) => {
    try {
      setIsUploading(true);

      // Update status to uploading
      setUploadedFiles(prev => prev.map(file =>
        file.id === fileId
          ? { ...file, status: 'uploading', progress: 10 }
          : file
      ));

      const fileObj = uploadedFiles.find(f => f.id === fileId);
      if (!fileObj) return;

      // Upload file to backend
      const uploadResponse = await apiService.uploadFile(fileObj.file);

      // Update with task ID and start monitoring
      setUploadedFiles(prev => prev.map(file =>
        file.id === fileId
          ? { ...file, taskId: uploadResponse.task_id, status: 'processing', progress: 20 }
          : file
      ));

      // Start monitoring progress
      monitorProgress(fileId, uploadResponse.task_id);

    } catch (error) {
      console.error('File processing error:', error);
      setUploadedFiles(prev => prev.map(file =>
        file.id === fileId
          ? { ...file, status: 'error', progress: 0, error: error.message }
          : file
      ));
    } finally {
      setIsUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: true
  });

  const monitorProgress = async (fileId, taskId) => {
    const checkStatus = async () => {
      try {
        const status = await apiService.getStatus(taskId);

        setUploadedFiles(prev => prev.map(file =>
          file.id === fileId
            ? {
                ...file,
                status: status.status,
                progress: status.progress || 0,
                message: status.message,
                results: status.results
              }
            : file
        ));

        if (status.status === 'completed') {
          setUploadComplete(true);
          setTimeout(() => setUploadComplete(false), 3000);
          return;
        } else if (status.status === 'error') {
          return;
        }

        // Continue monitoring if still processing
        setTimeout(checkStatus, 2000);
      } catch (error) {
        console.error('Status check error:', error);
        setUploadedFiles(prev => prev.map(file =>
          file.id === fileId
            ? { ...file, status: 'error', error: error.message }
            : file
        ));
      }
    };

    checkStatus();
  };

  const processUrl = async () => {
    if (!urlInput.trim()) return;

    const urlFileObj = {
      id: Date.now() + Math.random(),
      name: urlInput,
      size: 0,
      status: 'processing',
      progress: 10,
      uploadedAt: new Date(),
      taskId: null,
      type: 'url'
    };

    try {
      setIsUrlProcessing(true);

      setUploadedFiles(prev => [...prev, urlFileObj]);

      const response = await apiService.analyzeUrl(urlInput);

      setUploadedFiles(prev => prev.map(file =>
        file.id === urlFileObj.id
          ? { ...file, taskId: response.task_id, progress: 20 }
          : file
      ));

      monitorProgress(urlFileObj.id, response.task_id);
      setUrlInput('');

    } catch (error) {
      console.error('URL processing error:', error);
      setUploadedFiles(prev => prev.map(file =>
        file.id === urlFileObj.id
          ? { ...file, status: 'error', error: error.message }
          : file
      ));
    } finally {
      setIsUrlProcessing(false);
    }
  };

  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'uploading':
        return 'text-blue-600 bg-blue-100';
      case 'processing':
        return 'text-purple-600 bg-purple-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl lg:text-3xl font-bold text-accent">Upload Research Papers</h1>
          <p className="text-sm lg:text-base text-primary mt-1">
            Upload your research papers to generate AI models and APIs
          </p>
        </motion.div>

        {/* Success Message */}
        <AnimatePresence>
          {uploadComplete && (
            <motion.div
              className="mb-6 bg-green-50 border border-green-200 rounded-xl p-4"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-green-800 font-medium">Upload completed successfully!</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Upload Area */}
          <motion.div
            className="space-y-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Drag & Drop Area */}
            <motion.div
              className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 lg:p-8"
              variants={itemVariants}
            >
              <h2 className="text-lg lg:text-xl font-semibold text-accent mb-4">Upload Files</h2>
              
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300 ${
                  isDragActive 
                    ? 'border-highlight bg-blue-50' 
                    : 'border-gray-300 hover:border-highlight hover:bg-gray-50'
                }`}
              >
                <input {...getInputProps()} />
                <div className="space-y-4">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <div>
                    <p className="text-lg font-medium text-accent">
                      {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                    </p>
                    <p className="text-sm text-primary mt-1">
                      or <span className="text-highlight font-medium">browse files</span>
                    </p>
                  </div>
                  <div className="text-xs text-gray-500">
                    Supported formats: PDF (Max 50MB per file)
                  </div>
                </div>
              </div>
            </motion.div>

            {/* URL Input Section */}
            <motion.div
              className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
              variants={itemVariants}
            >
              <h2 className="text-lg lg:text-xl font-semibold text-accent mb-4">Analyze from URL</h2>

              <div className="space-y-4">
                <div>
                  <label htmlFor="url-input" className="block text-sm font-medium text-accent mb-2">
                    Paper URL (ArXiv, Research Gate, etc.)
                  </label>
                  <div className="flex space-x-2">
                    <input
                      id="url-input"
                      type="url"
                      value={urlInput}
                      onChange={(e) => setUrlInput(e.target.value)}
                      placeholder="https://arxiv.org/abs/2505.17303"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-highlight focus:border-transparent"
                      disabled={isUrlProcessing}
                    />
                    <button
                      onClick={processUrl}
                      disabled={!urlInput.trim() || isUrlProcessing}
                      className="px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isUrlProcessing ? (
                        <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      ) : (
                        'Analyze'
                      )}
                    </button>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  <p>• Supports ArXiv URLs (automatically converts to PDF)</p>
                  <p>• Direct PDF URLs from research platforms</p>
                  <p>• Processing may take 2-10 minutes depending on paper size</p>
                </div>
              </div>
            </motion.div>

            {/* Upload Guidelines */}
            <motion.div
              className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
              variants={itemVariants}
            >
              <h3 className="text-lg font-semibold text-accent mb-4">Upload Guidelines</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-accent">High-Quality Papers</p>
                    <p className="text-xs text-primary">Upload peer-reviewed research papers for best results</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-accent">Clear Text</p>
                    <p className="text-xs text-primary">Ensure text is readable and not image-based</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-accent">Complete Papers</p>
                    <p className="text-xs text-primary">Include methodology, results, and conclusions</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-accent">Data Sections</p>
                    <p className="text-xs text-primary">Papers with datasets generate better models</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Upload Queue */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
            variants={itemVariants}
            initial="hidden"
            animate="visible"
          >
            <h2 className="text-lg lg:text-xl font-semibold text-accent mb-4">
              Upload Queue ({uploadedFiles.length})
            </h2>
            
            {uploadedFiles.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p className="text-gray-500 mt-2">No files uploaded yet</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <AnimatePresence>
                  {uploadedFiles.map((file) => (
                    <motion.div
                      key={file.id}
                      className="border border-gray-200 rounded-lg p-4"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-accent truncate">
                            {file.type === 'url' ? 'URL Analysis' : file.name}
                          </p>
                          <p className="text-xs text-primary">
                            {file.type === 'url' ? file.name : `${formatFileSize(file.size)}`}
                          </p>

                          {file.message && (
                            <p className="text-xs text-gray-600 mt-1">
                              {file.message}
                            </p>
                          )}

                          {(file.status === 'uploading' || file.status === 'processing') && (
                            <div className="mt-2">
                              <div className="flex justify-between text-xs text-primary mb-1">
                                <span>{file.status === 'uploading' ? 'Uploading...' : 'Processing...'}</span>
                                <span>{file.progress}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <motion.div
                                  className="bg-highlight h-2 rounded-full"
                                  initial={{ width: 0 }}
                                  animate={{ width: `${file.progress}%` }}
                                  transition={{ duration: 0.3 }}
                                />
                              </div>
                            </div>
                          )}

                          {file.status === 'completed' && file.results && (
                            <div className="mt-2 p-2 bg-green-50 rounded-lg">
                              <p className="text-xs text-green-700 font-medium">Analysis Complete!</p>
                              <p className="text-xs text-green-600">
                                Keywords: {file.results.insights?.tfidf_keywords?.length || 0} •
                                Entities: {file.results.insights?.named_entities?.length || 0} •
                                Images: {file.results.images?.length || 0}
                              </p>
                            </div>
                          )}

                          {file.status === 'error' && (
                            <div className="mt-2 p-2 bg-red-50 rounded-lg">
                              <p className="text-xs text-red-700 font-medium">Processing Failed</p>
                              <p className="text-xs text-red-600">{file.error}</p>
                            </div>
                          )}
                        </div>
                        
                        <div className="ml-4 flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(file.status)}`}>
                            {file.status === 'uploading' ? 'Uploading' :
                             file.status === 'processing' ? 'Processing' :
                             file.status === 'completed' ? 'Completed' :
                             file.status === 'pending' ? 'Pending' : 'Error'}
                          </span>

                          {file.status === 'completed' && file.results && (
                            <button
                              onClick={() => window.open(`/dashboard/results/${file.taskId}`, '_blank')}
                              className="text-highlight hover:text-accent transition-colors duration-200 text-xs"
                              title="View Results"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            </button>
                          )}
                          
                          <button
                            onClick={() => removeFile(file.id)}
                            className="text-red-500 hover:text-red-700 transition-colors duration-200"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardUploadPaper;
