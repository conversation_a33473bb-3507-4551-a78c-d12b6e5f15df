import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const APIManagement = () => {
  const [apis, setApis] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [selectedApis, setSelectedApis] = useState([]);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState('');

  // Mock data for APIs
  useEffect(() => {
    const mockApis = [
      {
        id: 1,
        name: 'climate-prediction-api',
        title: 'Climate Prediction API',
        status: 'active',
        modelId: 1,
        modelName: 'Climate_CNN_v1',
        paperId: 1,
        paperTitle: 'Deep Learning Approaches for Climate Modeling',
        createdDate: '2024-01-15T14:30:00Z',
        lastUsed: '2024-01-16T09:15:00Z',
        version: '1.0.0',
        endpoint: 'https://api.sciml.com/v1/climate-prediction',
        method: 'POST',
        requests: 892,
        requestsToday: 45,
        avgResponseTime: '120ms',
        uptime: '99.8%',
        rateLimit: '1000/hour',
        description: 'Predict climate patterns and temperature changes using deep learning models',
        tags: ['Climate', 'Prediction', 'Temperature'],
        authentication: 'API Key',
        pricing: 'Free Tier',
        documentation: '/docs/climate-prediction-api'
      },
      {
        id: 2,
        name: 'weather-forecast-api',
        title: 'Weather Forecast API',
        status: 'active',
        modelId: 1,
        modelName: 'Climate_CNN_v1',
        paperId: 1,
        paperTitle: 'Deep Learning Approaches for Climate Modeling',
        createdDate: '2024-01-15T15:45:00Z',
        lastUsed: '2024-01-16T11:30:00Z',
        version: '1.1.0',
        endpoint: 'https://api.sciml.com/v1/weather-forecast',
        method: 'GET',
        requests: 1247,
        requestsToday: 78,
        avgResponseTime: '95ms',
        uptime: '99.9%',
        rateLimit: '500/hour',
        description: 'Generate accurate weather forecasts using AI-powered climate models',
        tags: ['Weather', 'Forecast', 'Climate'],
        authentication: 'API Key',
        pricing: 'Pro Tier',
        documentation: '/docs/weather-forecast-api'
      },
      {
        id: 3,
        name: 'protein-fold-api',
        title: 'Protein Folding Prediction API',
        status: 'active',
        modelId: 2,
        modelName: 'ProteinFold_Transformer',
        paperId: 3,
        paperTitle: 'Machine Learning for Protein Folding Prediction',
        createdDate: '2024-01-13T16:45:00Z',
        lastUsed: '2024-01-16T14:22:00Z',
        version: '2.0.0',
        endpoint: 'https://api.sciml.com/v1/protein-folding',
        method: 'POST',
        requests: 567,
        requestsToday: 23,
        avgResponseTime: '340ms',
        uptime: '99.7%',
        rateLimit: '100/hour',
        description: 'Predict protein secondary and tertiary structure from amino acid sequences',
        tags: ['Protein', 'Bioinformatics', 'Structure'],
        authentication: 'API Key',
        pricing: 'Enterprise',
        documentation: '/docs/protein-folding-api'
      },
      {
        id: 4,
        name: 'medical-image-api',
        title: 'Medical Image Analysis API',
        status: 'maintenance',
        modelId: 3,
        modelName: 'MedImage_ResNet',
        paperId: 5,
        paperTitle: 'Computer Vision for Medical Image Analysis',
        createdDate: '2024-01-11T18:20:00Z',
        lastUsed: '2024-01-15T16:45:00Z',
        version: '1.2.1',
        endpoint: 'https://api.sciml.com/v1/medical-image',
        method: 'POST',
        requests: 2341,
        requestsToday: 0,
        avgResponseTime: '250ms',
        uptime: '98.5%',
        rateLimit: '200/hour',
        description: 'Analyze medical images for tumor detection and classification',
        tags: ['Medical', 'Computer Vision', 'Diagnosis'],
        authentication: 'API Key',
        pricing: 'Pro Tier',
        documentation: '/docs/medical-image-api'
      },
      {
        id: 5,
        name: 'literature-analysis-api',
        title: 'Scientific Literature Analysis API',
        status: 'deprecated',
        modelId: 4,
        modelName: 'SciBERT_Classifier',
        paperId: 8,
        paperTitle: 'Natural Language Processing for Scientific Literature',
        createdDate: '2024-01-10T20:15:00Z',
        lastUsed: '2024-01-12T10:30:00Z',
        version: '1.0.0-deprecated',
        endpoint: 'https://api.sciml.com/v1/literature-analysis',
        method: 'POST',
        requests: 156,
        requestsToday: 0,
        avgResponseTime: '450ms',
        uptime: '95.2%',
        rateLimit: '50/hour',
        description: 'Extract insights and classify scientific literature using NLP',
        tags: ['NLP', 'Literature', 'Classification'],
        authentication: 'API Key',
        pricing: 'Free Tier',
        documentation: '/docs/literature-analysis-api'
      }
    ];

    setTimeout(() => {
      setApis(mockApis);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort APIs
  const filteredApis = apis
    .filter(api => {
      const matchesSearch = api.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           api.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           api.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           api.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesStatus = filterStatus === 'all' || api.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdDate) - new Date(a.createdDate);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'requests':
          return b.requests - a.requests;
        case 'uptime':
          return parseFloat(b.uptime) - parseFloat(a.uptime);
        default:
          return 0;
      }
    });

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'deprecated':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'maintenance':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'deprecated':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'inactive':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getPricingColor = (pricing) => {
    switch (pricing) {
      case 'Free Tier':
        return 'bg-blue-100 text-blue-800';
      case 'Pro Tier':
        return 'bg-purple-100 text-purple-800';
      case 'Enterprise':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSelectApi = (apiId) => {
    setSelectedApis(prev =>
      prev.includes(apiId)
        ? prev.filter(id => id !== apiId)
        : [...prev, apiId]
    );
  };

  const handleSelectAll = () => {
    if (selectedApis.length === filteredApis.length) {
      setSelectedApis([]);
    } else {
      setSelectedApis(filteredApis.map(api => api.id));
    }
  };

  const generateApiKey = () => {
    const key = 'sk-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    setNewApiKey(key);
    setShowApiKeyModal(true);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">API Management</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Manage and monitor your generated APIs and endpoints
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <button
                onClick={generateApiKey}
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                Generate API Key
              </button>
              <Link
                to="/dashboard/apis/create"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Create API
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Total APIs',
              value: apis.length,
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
                </svg>
              )
            },
            {
              label: 'Active APIs',
              value: apis.filter(api => api.status === 'active').length,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              )
            },
            {
              label: 'Total Requests',
              value: apis.reduce((sum, api) => sum + api.requests, 0).toLocaleString(),
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              )
            },
            {
              label: 'Requests Today',
              value: apis.reduce((sum, api) => sum + api.requestsToday, 0),
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="relative flex-1 lg:max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                placeholder="Search APIs, endpoints, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="deprecated">Deprecated</option>
                <option value="inactive">Inactive</option>
              </select>

              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight text-sm lg:text-base"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by Date</option>
                <option value="name">Sort by Name</option>
                <option value="requests">Sort by Requests</option>
                <option value="uptime">Sort by Uptime</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* APIs List */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading APIs...</p>
            </div>
          ) : filteredApis.length === 0 ? (
            <div className="p-8 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No APIs found</h3>
              <p className="mt-2 text-primary">
                {searchQuery || filterStatus !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'Upload papers to automatically generate APIs'
                }
              </p>
              {!searchQuery && filterStatus === 'all' && (
                <Link
                  to="/upload-paper"
                  className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                >
                  Upload Paper
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="bg-gray-50 px-4 lg:px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded"
                    checked={selectedApis.length === filteredApis.length}
                    onChange={handleSelectAll}
                  />
                  <span className="ml-3 text-sm font-medium text-accent">
                    {selectedApis.length > 0 ? `${selectedApis.length} selected` : 'Select all'}
                  </span>
                  {selectedApis.length > 0 && (
                    <div className="ml-4 flex space-x-2">
                      <button className="text-sm text-green-600 hover:text-green-800">Activate</button>
                      <button className="text-sm text-yellow-600 hover:text-yellow-800">Maintenance</button>
                      <button className="text-sm text-red-600 hover:text-red-800">Deprecate</button>
                    </div>
                  )}
                </div>
              </div>

              {/* Table Content */}
              <div className="divide-y divide-gray-200">
                <AnimatePresence>
                  {filteredApis.map((api, index) => (
                    <motion.div
                      key={api.id}
                      className="p-4 lg:p-6 hover:bg-gray-50 transition-colors duration-200"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <div className="flex items-start space-x-4">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-highlight focus:ring-highlight border-gray-300 rounded mt-1"
                          checked={selectedApis.includes(api.id)}
                          onChange={() => handleSelectApi(api.id)}
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3">
                                <h3 className="text-lg font-semibold text-accent hover:text-highlight transition-colors duration-200">
                                  <Link to={`/dashboard/apis/${api.id}`}>
                                    {api.title}
                                  </Link>
                                </h3>
                                <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-accent rounded-full">
                                  {api.method}
                                </span>
                                <span className="text-xs text-primary">v{api.version}</span>
                              </div>
                              <p className="text-sm text-primary mt-1">
                                <code className="bg-gray-100 px-2 py-1 rounded text-xs">{api.name}</code>
                              </p>
                              <p className="text-xs text-gray-500 mt-1">{api.description}</p>
                              <p className="text-xs text-primary mt-1">
                                Model: <Link to={`/dashboard/models/${api.modelId}`} className="text-highlight hover:text-accent">
                                  {api.modelName}
                                </Link> • Paper: <Link to={`/dashboard/papers/${api.paperId}`} className="text-highlight hover:text-accent">
                                  {api.paperTitle}
                                </Link>
                              </p>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {api.tags.map((tag, tagIndex) => (
                                  <span
                                    key={tagIndex}
                                    className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            </div>

                            <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(api.status)}`}>
                                {getStatusIcon(api.status)}
                                <span className="ml-1 capitalize">{api.status}</span>
                              </div>
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPricingColor(api.pricing)}`}>
                                {api.pricing}
                              </div>
                              <div className="text-xs text-primary">
                                Created: {new Date(api.createdDate).toLocaleDateString()}
                              </div>
                              <div className="text-xs text-primary">
                                Last used: {new Date(api.lastUsed).toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          {/* API Metrics */}
                          <div className="mt-4 grid grid-cols-1 lg:grid-cols-5 gap-4 text-sm">
                            <div className="lg:col-span-2">
                              <span className="text-primary">Endpoint:</span>
                              <div className="mt-1">
                                <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
                                  {api.endpoint}
                                </code>
                                <button
                                  onClick={() => copyToClipboard(api.endpoint)}
                                  className="ml-2 text-highlight hover:text-accent"
                                >
                                  <svg className="w-3 h-3 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                  </svg>
                                </button>
                              </div>
                            </div>
                            <div>
                              <span className="text-primary">Total Requests:</span>
                              <span className="ml-1 font-medium text-accent">{api.requests.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-primary">Today:</span>
                              <span className="ml-1 font-medium text-accent">{api.requestsToday}</span>
                            </div>
                            <div>
                              <span className="text-primary">Response Time:</span>
                              <span className="ml-1 font-medium text-accent">{api.avgResponseTime}</span>
                            </div>
                          </div>

                          {/* Rate Limiting & Auth */}
                          <div className="mt-4 grid grid-cols-1 lg:grid-cols-3 gap-4 text-sm">
                            <div className="bg-gray-50 rounded-lg p-3">
                              <span className="text-primary">Rate Limit:</span>
                              <span className="ml-1 font-medium text-accent">{api.rateLimit}</span>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-3">
                              <span className="text-primary">Authentication:</span>
                              <span className="ml-1 font-medium text-accent">{api.authentication}</span>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-3">
                              <span className="text-primary">Uptime:</span>
                              <span className="ml-1 font-medium text-accent">{api.uptime}</span>
                            </div>
                          </div>

                          <div className="mt-4 flex flex-wrap gap-2">
                            <Link
                              to={`/dashboard/apis/${api.id}`}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                            >
                              View Details
                            </Link>
                            <Link
                              to={api.documentation}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200"
                            >
                              Documentation
                            </Link>
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                              Test API
                            </button>
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-highlight hover:text-accent transition-colors duration-200">
                              Monitor
                            </button>
                            {api.status === 'active' && (
                              <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-yellow-600 hover:text-yellow-800 transition-colors duration-200">
                                Maintenance Mode
                              </button>
                            )}
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                              Deprecate
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </>
          )}
        </motion.div>

        {/* API Key Modal */}
        <AnimatePresence>
          {showApiKeyModal && (
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-white rounded-xl p-6 max-w-md w-full"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-accent">New API Key Generated</h3>
                  <button
                    onClick={() => setShowApiKeyModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <p className="text-sm text-primary mb-4">
                  Your new API key has been generated. Please copy it now as it won't be shown again.
                </p>
                <div className="bg-gray-100 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between">
                    <code className="text-sm font-mono text-accent break-all">{newApiKey}</code>
                    <button
                      onClick={() => copyToClipboard(newApiKey)}
                      className="ml-2 text-highlight hover:text-accent flex-shrink-0"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="flex justify-end">
                  <button
                    onClick={() => setShowApiKeyModal(false)}
                    className="px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                  >
                    Done
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </DashboardLayout>
  );
};

export default APIManagement;