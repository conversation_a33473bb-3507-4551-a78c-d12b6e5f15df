import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const TrainingModels = () => {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data for training models
  useEffect(() => {
    const mockTrainingModels = [
      {
        id: 2,
        name: 'ProteinFold_Transformer',
        type: 'Transformer',
        status: 'training',
        accuracy: 91.8,
        paperId: 3,
        paperTitle: 'Machine Learning for Protein Folding Prediction',
        createdDate: '2024-01-13T16:45:00Z',
        lastUpdated: '2024-01-14T10:20:00Z',
        version: '2.1.0',
        framework: 'PyTorch',
        size: '128.7 MB',
        tags: ['Protein', 'Transformer', 'Bioinformatics'],
        description: 'Transformer model for protein secondary structure prediction',
        training: {
          progress: 78,
          epoch: 156,
          totalEpochs: 200,
          eta: '2h 15m',
          currentLoss: 0.0234,
          bestLoss: 0.0187,
          learningRate: 0.0001,
          batchSize: 32,
          startTime: '2024-01-13T16:45:00Z',
          estimatedCompletion: '2024-01-14T14:35:00Z',
          gpuUtilization: 87,
          memoryUsage: '14.2 GB',
          stages: [
            { name: 'Data Loading', status: 'completed', duration: '5m 23s' },
            { name: 'Model Initialization', status: 'completed', duration: '2m 15s' },
            { name: 'Training', status: 'active', duration: '18h 42m' },
            { name: 'Validation', status: 'pending', duration: null },
            { name: 'Model Export', status: 'pending', duration: null }
          ]
        }
      },
      {
        id: 6,
        name: 'Climate_LSTM_v2',
        type: 'LSTM',
        status: 'training',
        accuracy: 89.4,
        paperId: 1,
        paperTitle: 'Deep Learning Approaches for Climate Modeling',
        createdDate: '2024-01-14T08:30:00Z',
        lastUpdated: '2024-01-14T12:15:00Z',
        version: '2.0.0',
        framework: 'TensorFlow',
        size: '67.3 MB',
        tags: ['Climate', 'LSTM', 'Time Series'],
        description: 'LSTM network for long-term climate pattern prediction',
        training: {
          progress: 45,
          epoch: 89,
          totalEpochs: 200,
          eta: '4h 32m',
          currentLoss: 0.0456,
          bestLoss: 0.0398,
          learningRate: 0.0005,
          batchSize: 64,
          startTime: '2024-01-14T08:30:00Z',
          estimatedCompletion: '2024-01-14T17:02:00Z',
          gpuUtilization: 92,
          memoryUsage: '8.7 GB',
          stages: [
            { name: 'Data Loading', status: 'completed', duration: '3m 45s' },
            { name: 'Model Initialization', status: 'completed', duration: '1m 30s' },
            { name: 'Training', status: 'active', duration: '3h 45m' },
            { name: 'Validation', status: 'pending', duration: null },
            { name: 'Model Export', status: 'pending', duration: null }
          ]
        }
      },
      {
        id: 7,
        name: 'MolecularGAN_v3',
        type: 'GAN',
        status: 'training',
        accuracy: 76.2,
        paperId: 2,
        paperTitle: 'Quantum Computing Applications in Drug Discovery',
        createdDate: '2024-01-14T14:20:00Z',
        lastUpdated: '2024-01-14T15:45:00Z',
        version: '3.0.0-beta',
        framework: 'PyTorch',
        size: '234.8 MB',
        tags: ['Molecular', 'GAN', 'Drug Discovery'],
        description: 'Generative adversarial network for novel molecule generation',
        training: {
          progress: 23,
          epoch: 46,
          totalEpochs: 200,
          eta: '12h 18m',
          currentLoss: 0.1234,
          bestLoss: 0.0987,
          learningRate: 0.0002,
          batchSize: 16,
          startTime: '2024-01-14T14:20:00Z',
          estimatedCompletion: '2024-01-15T03:38:00Z',
          gpuUtilization: 95,
          memoryUsage: '22.1 GB',
          stages: [
            { name: 'Data Loading', status: 'completed', duration: '8m 12s' },
            { name: 'Model Initialization', status: 'completed', duration: '4m 33s' },
            { name: 'Training', status: 'active', duration: '1h 25m' },
            { name: 'Validation', status: 'pending', duration: null },
            { name: 'Model Export', status: 'pending', duration: null }
          ]
        }
      }
    ];

    // Simulate real-time updates
    const interval = setInterval(() => {
      setModels(prevModels =>
        prevModels.map(model => ({
          ...model,
          training: {
            ...model.training,
            progress: Math.min(model.training.progress + Math.random() * 2, 100),
            epoch: model.training.epoch + Math.floor(Math.random() * 2),
            currentLoss: Math.max(model.training.currentLoss - Math.random() * 0.001, model.training.bestLoss)
          }
        }))
      );
    }, 3000);

    setTimeout(() => {
      setModels(mockTrainingModels);
      setLoading(false);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatDuration = (startTime) => {
    const start = new Date(startTime);
    const now = new Date();
    const diff = now - start;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const getStageIcon = (status) => {
    switch (status) {
      case 'completed':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'active':
        return (
          <svg className="w-4 h-4 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Training Models</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Monitor real-time training progress and performance metrics
              </p>
            </div>
            <div className="mt-4 lg:mt-0 flex space-x-3">
              <Link
                to="/dashboard/models"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
                All Models
              </Link>
              <Link
                to="/dashboard/models/create"
                className="inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Start Training
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Training Models',
              value: models.length,
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )
            },
            {
              label: 'Avg Progress',
              value: models.length > 0 ? Math.round(models.reduce((sum, model) => sum + model.training.progress, 0) / models.length) + '%' : '0%',
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              )
            },
            {
              label: 'Total Epochs',
              value: models.reduce((sum, model) => sum + model.training.epoch, 0),
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              )
            },
            {
              label: 'Avg GPU Usage',
              value: models.length > 0 ? Math.round(models.reduce((sum, model) => sum + model.training.gpuUtilization, 0) / models.length) + '%' : '0%',
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Training Models List */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {loading ? (
            <div className="bg-white rounded-xl p-8 text-center shadow-lg border border-gray-100">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-highlight mx-auto"></div>
              <p className="text-primary mt-2">Loading training models...</p>
            </div>
          ) : models.length === 0 ? (
            <div className="bg-white rounded-xl p-8 text-center shadow-lg border border-gray-100">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-accent">No models currently training</h3>
              <p className="mt-2 text-primary">Start training a new model to see real-time progress here</p>
              <Link
                to="/dashboard/models/create"
                className="mt-4 inline-flex items-center px-4 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
              >
                Start Training
              </Link>
            </div>
          ) : (
            <AnimatePresence>
              {models.map((model, index) => (
                <motion.div
                  key={model.id}
                  className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-6">
                    {/* Model Header */}
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-semibold text-accent">
                            <Link to={`/dashboard/models/${model.id}`} className="hover:text-highlight transition-colors duration-200">
                              {model.name}
                            </Link>
                          </h3>
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {model.type}
                          </span>
                          <span className="text-sm text-primary">v{model.version}</span>
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{model.description}</p>
                        <p className="text-sm text-primary">
                          From: <Link to={`/dashboard/papers/${model.paperId}`} className="text-highlight hover:text-accent">
                            {model.paperTitle}
                          </Link>
                        </p>
                        <div className="flex flex-wrap gap-2 mt-3">
                          {model.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="px-2 py-1 text-xs bg-gray-100 text-accent rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col lg:items-end space-y-2">
                        <div className="text-sm text-primary">
                          Started: {formatDuration(model.training.startTime)} ago
                        </div>
                        <div className="text-sm text-primary">
                          ETA: {model.training.eta}
                        </div>
                        <div className="text-sm font-medium text-highlight">
                          {model.training.progress.toFixed(1)}% Complete
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-6">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-accent">Training Progress</span>
                        <span className="text-sm text-primary">
                          Epoch {model.training.epoch}/{model.training.totalEpochs}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <motion.div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${model.training.progress}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>

                    {/* Training Metrics */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-primary">Current Loss</div>
                        <div className="text-lg font-semibold text-accent">{model.training.currentLoss.toFixed(4)}</div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-primary">Best Loss</div>
                        <div className="text-lg font-semibold text-green-600">{model.training.bestLoss.toFixed(4)}</div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-primary">Learning Rate</div>
                        <div className="text-lg font-semibold text-accent">{model.training.learningRate}</div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-primary">Batch Size</div>
                        <div className="text-lg font-semibold text-accent">{model.training.batchSize}</div>
                      </div>
                    </div>

                    {/* Resource Usage */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                      <div className="bg-yellow-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-yellow-800">GPU Utilization</span>
                          <span className="text-sm text-yellow-600">{model.training.gpuUtilization}%</span>
                        </div>
                        <div className="w-full bg-yellow-200 rounded-full h-2">
                          <div
                            className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${model.training.gpuUtilization}%` }}
                          />
                        </div>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-purple-800">Memory Usage</span>
                          <span className="text-sm text-purple-600">{model.training.memoryUsage}</span>
                        </div>
                        <div className="text-xs text-purple-600 mt-1">
                          Framework: {model.framework}
                        </div>
                      </div>
                    </div>

                    {/* Training Stages */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-accent mb-3">Training Stages</h4>
                      <div className="space-y-2">
                        {model.training.stages.map((stage, stageIndex) => (
                          <div key={stageIndex} className="flex items-center space-x-3">
                            {getStageIcon(stage.status)}
                            <span className={`text-sm ${
                              stage.status === 'completed' ? 'text-green-600' :
                              stage.status === 'active' ? 'text-blue-600' :
                              'text-gray-500'
                            }`}>
                              {stage.name}
                            </span>
                            {stage.duration && (
                              <span className="text-xs text-gray-500">({stage.duration})</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                      <Link
                        to={`/dashboard/models/${model.id}`}
                        className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200"
                      >
                        View Details
                      </Link>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-highlight hover:text-accent transition-colors duration-200">
                        View Logs
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-yellow-600 hover:text-yellow-800 transition-colors duration-200">
                        Pause Training
                      </button>
                      <button className="inline-flex items-center px-3 py-1 text-sm font-medium text-red-600 hover:text-red-800 transition-colors duration-200">
                        Stop Training
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default TrainingModels;