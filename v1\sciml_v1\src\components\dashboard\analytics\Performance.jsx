import { useState } from 'react';
import { motion } from 'framer-motion';
import DashboardLayout from '../../DashboardLayout';

const Performance = () => {
  const [timeRange, setTimeRange] = useState('week');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // Mock data for different time ranges
  const mockData = {
    week: {
      responseTime: 245,
      successRate: 99.8,
      errorRate: 0.2,
      uptime: 99.99,
      responseTimeTrend: [280, 265, 250, 245, 240, 235, 245],
      errorDistribution: [
        { type: 'API Timeout', count: 12 },
        { type: 'Validation Error', count: 8 },
        { type: 'Server Error', count: 5 },
        { type: 'Network Error', count: 3 }
      ]
    },
    month: {
      responseTime: 250,
      successRate: 99.5,
      errorRate: 0.5,
      uptime: 99.95,
      responseTimeTrend: [280, 275, 270, 265, 260, 255, 250, 245, 240, 235, 240, 245, 250, 255, 260, 265, 270, 275, 280, 275, 270, 265, 260, 255, 250, 245, 240, 235, 240, 245],
      errorDistribution: [
        { type: 'API Timeout', count: 45 },
        { type: 'Validation Error', count: 30 },
        { type: 'Server Error', count: 20 },
        { type: 'Network Error', count: 15 }
      ]
    },
    year: {
      responseTime: 255,
      successRate: 99.2,
      errorRate: 0.8,
      uptime: 99.9,
      responseTimeTrend: [280, 275, 270, 265, 260, 255, 250, 245, 240, 235, 240, 245],
      errorDistribution: [
        { type: 'API Timeout', count: 180 },
        { type: 'Validation Error', count: 120 },
        { type: 'Server Error', count: 80 },
        { type: 'Network Error', count: 60 }
      ]
    }
  };

  const currentData = mockData[timeRange];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-2xl lg:text-3xl font-bold text-accent">Performance Metrics</h1>
          <p className="mt-2 text-primary">Monitor system performance and health</p>
        </motion.div>

        {/* Time Range Selector */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 mb-8 p-4">
          <div className="flex flex-wrap gap-2">
            {['week', 'month', 'year'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  timeRange === range
                    ? 'bg-highlight text-white'
                    : 'bg-gray-100 text-primary hover:bg-gray-200'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Performance Overview */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {[
            {
              label: 'Avg Response Time',
              value: `${currentData.responseTime}ms`,
              color: 'bg-blue-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            },
            {
              label: 'Success Rate',
              value: `${currentData.successRate}%`,
              color: 'bg-green-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            },
            {
              label: 'Error Rate',
              value: `${currentData.errorRate}%`,
              color: 'bg-red-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )
            },
            {
              label: 'System Uptime',
              value: `${currentData.uptime}%`,
              color: 'bg-purple-500',
              icon: (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              )
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-4 lg:p-6 shadow-lg border border-gray-100"
              variants={itemVariants}
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-lg ${stat.color} flex items-center justify-center text-white mr-3`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-xs lg:text-sm text-primary">{stat.label}</p>
                  <p className="text-lg lg:text-2xl font-bold text-accent">{stat.value}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Performance Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Response Time Trend */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
            variants={itemVariants}
          >
            <h2 className="text-lg font-semibold text-accent mb-6">Response Time Trend</h2>
            <div className="h-64 flex items-end space-x-2">
              {currentData.responseTimeTrend.map((value, index) => (
                <div
                  key={index}
                  className="flex-1 bg-blue-500/20 rounded-t-lg hover:bg-blue-500/30 transition-colors duration-200"
                  style={{ height: `${(value / 300) * 100}%` }}
                >
                  <div className="h-full bg-blue-500 rounded-t-lg" style={{ height: `${(value / 300) * 100}%` }}></div>
                </div>
              ))}
            </div>
            <div className="mt-4 flex justify-between text-xs text-primary">
              <span>Start</span>
              <span>End</span>
            </div>
          </motion.div>

          {/* Error Distribution */}
          <motion.div
            className="bg-white rounded-xl shadow-lg border border-gray-100 p-6"
            variants={itemVariants}
          >
            <h2 className="text-lg font-semibold text-accent mb-6">Error Distribution</h2>
            <div className="space-y-4">
              {currentData.errorDistribution.map((error, index) => (
                <div key={index}>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-primary">{error.type}</span>
                    <span className="text-accent">{error.count} errors</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${(error.count / Math.max(...currentData.errorDistribution.map(e => e.count))) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* System Health */}
        <motion.div
          className="bg-white rounded-xl shadow-lg border border-gray-100"
          variants={itemVariants}
        >
          <div className="p-6">
            <h2 className="text-lg font-semibold text-accent mb-6">System Health</h2>
            <div className="space-y-4">
              {[
                { component: 'API Gateway', status: 'Healthy', lastCheck: '2 minutes ago' },
                { component: 'Database', status: 'Healthy', lastCheck: '5 minutes ago' },
                { component: 'Cache Layer', status: 'Healthy', lastCheck: '1 minute ago' },
                { component: 'Storage System', status: 'Healthy', lastCheck: '3 minutes ago' }
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-accent">{item.component}</p>
                      <p className="text-sm text-primary">Last check: {item.lastCheck}</p>
                    </div>
                  </div>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                    {item.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default Performance; 