/* Auth.css - Styles for Login and Signup components */

/* Container styles */
.auth-container {
  background: linear-gradient(135deg, rgba(0, 63, 92, 0.95), rgba(47, 75, 124, 0.95));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Form background */
.auth-form-bg {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Input field styles */
.auth-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.auth-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(0, 181, 173, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 181, 173, 0.2);
}

.auth-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Button styles */
.auth-button {
  background: linear-gradient(to right, #00b5ad, #2f4b7c);
  border: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.auth-button:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.5);
  transform: translateY(-2px);
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.auth-button:hover::before {
  left: 100%;
}

/* Social login buttons */
.social-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.social-button.google:hover {
  box-shadow: 0 0 10px rgba(234, 67, 53, 0.5);
}

.social-button.github:hover {
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.social-button.linkedin:hover {
  box-shadow: 0 0 10px rgba(0, 119, 181, 0.5);
}

/* Decorative elements */
.auth-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(to right, #00b5ad, #2f4b7c);
  opacity: 0.1;
  filter: blur(40px);
}

/* Form switch animation */
.form-switch-enter {
  opacity: 0;
  transform: translateX(20px);
}

.form-switch-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.form-switch-exit {
  opacity: 1;
  transform: translateX(0);
}

.form-switch-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Floating labels */
.floating-label {
  position: absolute;
  pointer-events: none;
  left: 12px;
  top: 12px;
  transition: 0.2s ease all;
  color: rgba(255, 255, 255, 0.5);
}

.auth-input:focus ~ .floating-label,
.auth-input:not(:placeholder-shown) ~ .floating-label {
  top: -10px;
  left: 10px;
  font-size: 12px;
  color: #00b5ad;
  background: rgba(47, 75, 124, 0.8);
  padding: 0 5px;
  border-radius: 4px;
}

/* Password strength indicator */
.password-strength {
  height: 4px;
  border-radius: 2px;
  margin-top: 5px;
  transition: all 0.3s ease;
}

.strength-weak {
  background: linear-gradient(to right, #ff4d4d, transparent);
  width: 25%;
}

.strength-medium {
  background: linear-gradient(to right, #ffaa00, transparent);
  width: 50%;
}

.strength-strong {
  background: linear-gradient(to right, #00cc44, transparent);
  width: 75%;
}

.strength-very-strong {
  background: linear-gradient(to right, #00b5ad, #2f4b7c);
  width: 100%;
}
