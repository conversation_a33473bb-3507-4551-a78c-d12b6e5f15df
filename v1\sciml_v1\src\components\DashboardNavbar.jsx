import { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import UserDropdown from './UserDropdown';

const DashboardNavbar = ({ onToggleSidebar, isSidebarOpen }) => {
  const [notifications] = useState([
    { id: 1, message: 'New paper analysis completed', time: '2 min ago', unread: true },
    { id: 2, message: 'Model training finished', time: '1 hour ago', unread: true },
    { id: 3, message: 'API usage limit warning', time: '3 hours ago', unread: false }
  ]);

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <motion.nav
      className="bg-white shadow-lg border-b border-gray-200 fixed w-full z-40"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Left side */}
          <div className="flex items-center">
            {/* Sidebar toggle button - only on mobile */}
            <motion.button
              onClick={onToggleSidebar}
              className="p-2 rounded-md text-accent hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-highlight lg:hidden"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </motion.button>

            {/* Logo and title */}
            <div className="flex items-center ml-2 lg:ml-0">
              <Link to="/" className="flex items-center">
                <img className="h-6 lg:h-8 w-auto mr-2 lg:mr-3" src="/logo_2.png" alt="SciML Logo" />
                <span className="text-lg lg:text-xl font-bold text-accent hidden sm:block">SciML Dashboard</span>
                <span className="text-lg font-bold text-accent sm:hidden">SciML</span>
              </Link>
            </div>
          </div>

          {/* Center - Search bar */}
          <div className="hidden md:flex items-center flex-1 max-w-md lg:max-w-lg mx-4 lg:mx-8">
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-8 lg:pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm"
                placeholder="Search..."
              />
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-2 lg:space-x-4">
            {/* Notifications */}
            <div className="relative">
              <motion.button
                className="p-2 text-accent hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-highlight"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 1.5v1h-13v-1L6.5 11.5v-2a6 6 0 0 1 6-6z" />
                </svg>
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 lg:h-5 lg:w-5 flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </motion.button>
            </div>

            {/* Quick actions */}
            <Link to="/upload-paper">
              <motion.button
                className="hidden lg:inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-highlight hover:bg-opacity-90 transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Upload Paper
              </motion.button>
            </Link>

            {/* Mobile upload button */}
            <Link to="/upload-paper" className="lg:hidden">
              <motion.button
                className="p-2 text-accent hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-highlight"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
              </motion.button>
            </Link>

            {/* User dropdown */}
            <UserDropdown />
          </div>
        </div>
      </div>

      {/* Mobile search bar */}
      <div className="md:hidden px-4 pb-3">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            className="block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-highlight focus:border-highlight text-sm"
            placeholder="Search papers, models, or data..."
          />
        </div>
      </div>
    </motion.nav>
  );
};

export default DashboardNavbar;
