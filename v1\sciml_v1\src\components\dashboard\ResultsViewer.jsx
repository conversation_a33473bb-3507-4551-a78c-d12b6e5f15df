import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const API_BASE_URL = 'http://localhost:5000';

const ResultsViewer = () => {
  const { taskId } = useParams();
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchResults = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/results/${taskId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch results');
        }
        const data = await response.json();
        setResults(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (taskId) {
      fetchResults();
    }
  }, [taskId]);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'keywords', label: 'Keywords', icon: '🔑' },
    { id: 'entities', label: 'Entities', icon: '🏷️' },
    { id: 'images', label: 'Images', icon: '🖼️' },
    { id: 'tables', label: 'Tables', icon: '📋' },
    { id: 'metadata', label: 'Metadata', icon: '📄' }
  ];

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-highlight mx-auto"></div>
            <p className="text-primary mt-4">Loading results...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
            <svg className="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-red-800">Error Loading Results</h3>
            <p className="mt-2 text-red-600">{error}</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4 lg:p-6">
        {/* Header */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl lg:text-3xl font-bold text-accent">Analysis Results</h1>
          <p className="text-sm lg:text-base text-primary mt-1">
            {results?.metadata?.title || 'Document Analysis'}
          </p>
        </motion.div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-highlight text-highlight'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'overview' && (
              <OverviewTab results={results} />
            )}
            {activeTab === 'keywords' && (
              <KeywordsTab keywords={results?.insights?.tfidf_keywords || []} />
            )}
            {activeTab === 'entities' && (
              <EntitiesTab entities={results?.insights?.named_entities || []} />
            )}
            {activeTab === 'images' && (
              <ImagesTab images={results?.images || []} />
            )}
            {activeTab === 'tables' && (
              <TablesTab tables={results?.tables || []} />
            )}
            {activeTab === 'metadata' && (
              <MetadataTab metadata={results?.metadata || {}} />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </DashboardLayout>
  );
};

// Overview Tab Component
const OverviewTab = ({ results }) => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
      <h3 className="text-lg font-semibold text-accent mb-4">Document Summary</h3>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-primary">Title:</span>
          <span className="font-medium text-accent">{results?.metadata?.title || 'N/A'}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Author:</span>
          <span className="font-medium text-accent">{results?.metadata?.author || 'N/A'}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Pages:</span>
          <span className="font-medium text-accent">{results?.metadata?.pages || 0}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Keywords Found:</span>
          <span className="font-medium text-accent">{results?.insights?.tfidf_keywords?.length || 0}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Entities Found:</span>
          <span className="font-medium text-accent">{results?.insights?.named_entities?.length || 0}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Images:</span>
          <span className="font-medium text-accent">{results?.images?.length || 0}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-primary">Tables:</span>
          <span className="font-medium text-accent">{results?.tables?.length || 0}</span>
        </div>
      </div>
    </div>

    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
      <h3 className="text-lg font-semibold text-accent mb-4">Top Keywords</h3>
      <div className="space-y-2">
        {results?.insights?.tfidf_keywords?.slice(0, 10).map(([keyword, score], index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-accent">{keyword}</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-highlight h-2 rounded-full" 
                  style={{ width: `${(score * 100)}%` }}
                />
              </div>
              <span className="text-xs text-primary">{(score * 100).toFixed(1)}%</span>
            </div>
          </div>
        )) || <p className="text-gray-500">No keywords found</p>}
      </div>
    </div>
  </div>
);

// Keywords Tab Component
const KeywordsTab = ({ keywords }) => (
  <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
    <h3 className="text-lg font-semibold text-accent mb-4">TF-IDF Keywords</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {keywords.map(([keyword, score], index) => (
        <div key={index} className="p-3 bg-gray-50 rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium text-accent">{keyword}</span>
            <span className="text-xs text-primary">{(score * 100).toFixed(2)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-highlight h-2 rounded-full" 
              style={{ width: `${(score * 100)}%` }}
            />
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Entities Tab Component
const EntitiesTab = ({ entities }) => {
  const groupedEntities = entities.reduce((acc, entity) => {
    const label = entity.label || 'OTHER';
    if (!acc[label]) acc[label] = [];
    acc[label].push(entity);
    return acc;
  }, {});

  return (
    <div className="space-y-6">
      {Object.entries(groupedEntities).map(([label, entityList]) => (
        <div key={label} className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-accent mb-4">{label}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {entityList.map((entity, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <p className="font-medium text-accent">{entity.text}</p>
                <p className="text-xs text-primary">{entity.description}</p>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Images Tab Component
const ImagesTab = ({ images }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {images.map((image, index) => (
      <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
        <div className="aspect-w-16 aspect-h-9 mb-4">
          <img 
            src={`data:image/png;base64,${image.data}`}
            alt={`Image ${index + 1}`}
            className="w-full h-48 object-contain bg-gray-50 rounded-lg"
          />
        </div>
        <div className="space-y-2">
          <p className="text-sm font-medium text-accent">Page {image.page}</p>
          <p className="text-xs text-primary">Type: {image.type}</p>
          <p className="text-xs text-primary">Size: {image.width} × {image.height}</p>
          {image.ocr_text && (
            <div className="mt-2 p-2 bg-gray-50 rounded">
              <p className="text-xs text-gray-600">OCR Text:</p>
              <p className="text-xs text-accent">{image.ocr_text}</p>
            </div>
          )}
        </div>
      </div>
    ))}
  </div>
);

// Tables Tab Component
const TablesTab = ({ tables }) => (
  <div className="space-y-6">
    {tables.map((table, index) => (
      <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-accent mb-4">
          Table {index + 1} (Page {table.page})
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {table.headers?.map((header, headerIndex) => (
                  <th key={headerIndex} className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {table.data?.slice(1).map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <td key={cellIndex} className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    ))}
  </div>
);

// Metadata Tab Component
const MetadataTab = ({ metadata }) => (
  <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
    <h3 className="text-lg font-semibold text-accent mb-4">Document Metadata</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Object.entries(metadata).map(([key, value]) => (
        <div key={key} className="p-3 bg-gray-50 rounded-lg">
          <p className="text-sm font-medium text-accent capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
          <p className="text-sm text-primary">{value || 'N/A'}</p>
        </div>
      ))}
    </div>
  </div>
);

export default ResultsViewer;
