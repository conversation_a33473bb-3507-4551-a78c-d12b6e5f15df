import { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Navbar from './Navbar';
import Footer from './Footer';
import './styles/Auth.css';

const Login = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: { duration: 0.4 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    setError('');

    const result = await login(email, password);

    if (result.success) {
      console.log('Login successful!');
      navigate('/dashboard'); // Redirect to dashboard after successful login
    } else {
      setError(result.error || 'Invalid email or password. Please try again.');
    }

    setIsLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center py-12 lg:py-20 px-4 bg-gradient-to-b from-primary to-accent">
        {/* Decorative elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-highlight opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 lg:w-96 lg:h-96 bg-primary opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <motion.div
          className="w-full max-w-md relative z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <div className="relative z-10 auth-container rounded-xl p-4 sm:p-6 lg:p-8 shadow-xl">
            <motion.div className="text-center mb-6 lg:mb-8" variants={itemVariants}>
              <h2 className="text-2xl lg:text-3xl font-bold text-white">Welcome Back</h2>
              <p className="mt-2 text-gray-300 text-sm lg:text-base">Sign in to your SciML account</p>
            </motion.div>

            {error && (
              <motion.div
                className="mb-6 p-3 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-md text-white text-sm"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {error}
              </motion.div>
            )}

            <form onSubmit={handleSubmit}>
              <motion.div className="mb-4 lg:mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    className="auth-input w-full px-3 lg:px-4 py-2 lg:py-3 rounded-lg focus:outline-none text-sm lg:text-base"
                    placeholder=" "
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                  <label htmlFor="email" className="floating-label">Email Address</label>
                </div>
              </motion.div>

              <motion.div className="mb-4 lg:mb-6" variants={itemVariants}>
                <div className="relative">
                  <input
                    type="password"
                    id="password"
                    className="auth-input w-full px-3 lg:px-4 py-2 lg:py-3 rounded-lg focus:outline-none text-sm lg:text-base"
                    placeholder=" "
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                  <label htmlFor="password" className="floating-label">Password</label>
                </div>
              </motion.div>

              <motion.div className="flex items-center justify-between mb-4 lg:mb-6" variants={itemVariants}>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember-me"
                    className="h-4 w-4 bg-transparent border-2 border-gray-400 rounded focus:ring-highlight focus:ring-offset-0"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <label htmlFor="remember-me" className="ml-2 text-xs lg:text-sm text-gray-300">
                    Remember me
                  </label>
                </div>

                <div>
                  <a href="#forgot-password" className="text-xs lg:text-sm text-highlight hover:text-white transition-colors duration-200">
                    Forgot password?
                  </a>
                </div>
              </motion.div>

              <motion.div variants={itemVariants}>
                <button
                  type="submit"
                  className="auth-button w-full py-2 lg:py-3 px-4 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-highlight focus:ring-opacity-50 text-sm lg:text-base"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Signing in...
                    </span>
                  ) : (
                    'Sign In'
                  )}
                </button>
              </motion.div>
            </form>

            <motion.div className="mt-6 lg:mt-8 text-center" variants={itemVariants}>
              <p className="text-gray-300 text-xs lg:text-sm mb-3 lg:mb-4">Or sign in with</p>
              <div className="flex justify-center space-x-3 lg:space-x-4">
                <motion.button
                  className="social-button google p-2 rounded-full"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21.8055 10.0415H21V10H12V14H17.6515C16.827 16.3285 14.6115 18 12 18C8.6865 18 6 15.3135 6 12C6 8.6865 8.6865 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C6.4775 2 2 6.4775 2 12C2 17.5225 6.4775 22 12 22C17.5225 22 22 17.5225 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z" fill="#FFC107"/>
                    <path d="M3.15302 7.3455L6.43852 9.755C7.32752 7.554 9.48052 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C8.15902 2 4.82802 4.1685 3.15302 7.3455Z" fill="#FF3D00"/>
                    <path d="M12 22C14.583 22 16.93 21.0115 18.7045 19.404L15.6095 16.785C14.5718 17.5742 13.3038 18.001 12 18C9.39897 18 7.19047 16.3415 6.35847 14.027L3.09747 16.5395C4.75247 19.778 8.11347 22 12 22Z" fill="#4CAF50"/>
                    <path d="M21.8055 10.0415H21V10H12V14H17.6515C17.2571 15.1082 16.5467 16.0766 15.608 16.7855L15.6095 16.7845L18.7045 19.4035C18.4855 19.6025 22 17 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z" fill="#1976D2"/>
                  </svg>
                </motion.button>

                <motion.button
                  className="social-button github p-2 rounded-full"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.477 2 2 6.477 2 12C2 16.418 4.865 20.166 8.839 21.489C9.339 21.581 9.521 21.278 9.521 21.016C9.521 20.782 9.512 20.082 9.508 19.273C6.726 19.866 6.139 17.962 6.139 17.962C5.685 16.811 5.028 16.509 5.028 16.509C4.128 15.88 5.095 15.894 5.095 15.894C6.092 15.962 6.626 16.926 6.626 16.926C7.521 18.4 8.97 18.008 9.539 17.755C9.631 17.12 9.889 16.729 10.175 16.519C7.955 16.305 5.62 15.473 5.62 11.658C5.62 10.594 6.01 9.727 6.646 9.058C6.543 8.809 6.2 7.786 6.746 6.346C6.746 6.346 7.586 6.081 9.497 7.441C10.3 7.222 11.15 7.112 12 7.109C12.85 7.112 13.7 7.222 14.503 7.441C16.414 6.081 17.254 6.346 17.254 6.346C17.8 7.786 17.457 8.809 17.354 9.058C17.99 9.727 18.38 10.594 18.38 11.658C18.38 15.483 16.045 16.302 13.815 16.511C14.172 16.773 14.491 17.293 14.491 18.086C14.491 19.234 14.479 20.694 14.479 21.016C14.479 21.281 14.659 21.587 15.167 21.487C19.138 20.161 22 16.416 22 12C22 6.477 17.523 2 12 2Z"/>
                  </svg>
                </motion.button>

                <motion.button
                  className="social-button linkedin p-2 rounded-full"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 24 24" fill="#0077B5" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 3C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19ZM18.5 18.5V13.2C18.5 12.3354 18.1565 11.5062 17.5452 10.8948C16.9338 10.2835 16.1046 9.94 15.24 9.94C14.39 9.94 13.4 10.46 12.92 11.24V10.13H10.13V18.5H12.92V13.57C12.92 12.8 13.54 12.17 14.31 12.17C14.6813 12.17 15.0374 12.3175 15.2999 12.5801C15.5625 12.8426 15.71 13.1987 15.71 13.57V18.5H18.5ZM6.88 8.56C7.32556 8.56 7.75288 8.383 8.06794 8.06794C8.383 7.75288 8.56 7.32556 8.56 6.88C8.56 5.95 7.81 5.19 6.88 5.19C6.43178 5.19 6.00193 5.36805 5.68499 5.68499C5.36805 6.00193 5.19 6.43178 5.19 6.88C5.19 7.81 5.95 8.56 6.88 8.56ZM8.27 18.5V10.13H5.5V18.5H8.27Z"/>
                  </svg>
                </motion.button>
              </div>
            </motion.div>

            <motion.div className="mt-6 lg:mt-8 text-center" variants={itemVariants}>
              <p className="text-gray-300 text-sm lg:text-base">
                Don't have an account?{' '}
                <Link
                  to="/signup"
                  className="text-highlight hover:text-white transition-colors duration-200 font-medium"
                >
                  Sign up
                </Link>
              </p>
            </motion.div>
          </div>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
};

export default Login;
