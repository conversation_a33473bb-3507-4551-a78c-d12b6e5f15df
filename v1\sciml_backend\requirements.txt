# SciML Backend Requirements
# Core web framework
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7

# PDF processing
PyMuPDF==1.23.8
pdfplumber==0.10.3

# Web scraping and requests
requests==2.31.0
beautifulsoup4==4.12.2

# Data processing
pandas==2.1.4
numpy==1.24.4

# Image processing
opencv-python==4.8.1.78
Pillow==10.1.0

# NLP libraries
spacy==3.7.2
nltk==3.8.1
textstat==0.7.3

# Machine Learning
scikit-learn==1.3.2

# OCR (optional - web-compatible)
easyocr==1.7.0

# Alternative OCR (if needed)
# pytesseract==0.3.10

# Utilities
pathlib2==2.3.7
python-dateutil==2.8.2

# Development and testing
pytest==7.4.3
pytest-flask==1.3.0
