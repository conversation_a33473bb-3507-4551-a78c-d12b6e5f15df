/* Navbar.css - Styles for the Navbar component */

/* Navbar background gradient */
.navbar-gradient {
  background: linear-gradient(to right, rgba(0, 63, 92, 0.95), rgba(47, 75, 124, 0.95));
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.navbar-transparent {
  background: rgba(0, 63, 92, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Active link indicator */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #00b5ad, #4cc9f0);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
  opacity: 1;
}

/* Button glow effect */
.btn-glow:hover {
  box-shadow: 0 0 15px rgba(0, 181, 173, 0.6);
}

/* Mobile menu animation */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.mobile-menu-exit {
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms, transform 300ms;
}

/* Logo animation */
@keyframes logo-pulse {
  0% {
    filter: drop-shadow(0 0 2px rgba(0, 181, 173, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(0, 181, 173, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(0, 181, 173, 0.4));
  }
}

.logo-pulse {
  animation: logo-pulse 3s infinite ease-in-out;
}

/* Dropdown menu styles */
.dropdown-menu {
  background: rgba(0, 63, 92, 0.95);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.dropdown-item {
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: rgba(0, 181, 173, 0.1);
  transform: translateX(5px);
}
