#!/usr/bin/env python3
"""
SciML Platform - Production Web Application
Production version with debug mode disabled and optimizations
"""

# Import the main app
from app import app, platform

if __name__ == '__main__':
    # Create storage directories
    import os
    from pathlib import Path
    
    STORAGE_DIR = Path('storage')
    STATUS_DIR = STORAGE_DIR / 'status'
    RESULTS_DIR = STORAGE_DIR / 'results'
    
    STORAGE_DIR.mkdir(exist_ok=True)
    STATUS_DIR.mkdir(exist_ok=True)
    RESULTS_DIR.mkdir(exist_ok=True)
    
    print("🚀 Starting SciML Platform Web Application (Production Mode)...")
    print("📱 Access the application at: http://localhost:5000")
    print("🔧 Health check available at: http://localhost:5000/health")
    print("⚠️  Debug mode is DISABLED for production")
    
    # Run in production mode
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
