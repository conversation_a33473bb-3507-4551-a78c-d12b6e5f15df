import { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';

const CreateModel = () => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    modelType: '',
    framework: '',
    paperId: '',
    hyperparameters: {
      learningRate: 0.001,
      batchSize: 32,
      epochs: 100,
      optimizer: 'adam'
    },
    deploymentConfig: {
      autoScale: true,
      minInstances: 1,
      maxInstances: 10,
      region: 'us-east-1'
    }
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const modelTypes = [
    { value: 'cnn', label: 'Convolutional Neural Network (CNN)', description: 'Best for image and spatial data analysis' },
    { value: 'rnn', label: 'Recurrent Neural Network (RNN)', description: 'Ideal for sequential and time-series data' },
    { value: 'lstm', label: 'Long Short-Term Memory (LSTM)', description: 'Advanced RNN for long-term dependencies' },
    { value: 'transformer', label: 'Transformer', description: 'State-of-the-art for NLP and attention mechanisms' },
    { value: 'resnet', label: 'Residual Network (ResNet)', description: 'Deep CNN with skip connections' },
    { value: 'bert', label: 'BERT', description: 'Bidirectional encoder for language understanding' },
    { value: 'gan', label: 'Generative Adversarial Network (GAN)', description: 'For generating new data samples' },
    { value: 'autoencoder', label: 'Autoencoder', description: 'For dimensionality reduction and feature learning' }
  ];

  const frameworks = [
    { value: 'tensorflow', label: 'TensorFlow', description: 'Google\'s open-source ML framework' },
    { value: 'pytorch', label: 'PyTorch', description: 'Facebook\'s dynamic neural network framework' },
    { value: 'keras', label: 'Keras', description: 'High-level neural networks API' },
    { value: 'scikit-learn', label: 'Scikit-learn', description: 'Simple and efficient ML tools' },
    { value: 'huggingface', label: 'Hugging Face', description: 'Transformers and NLP models' },
    { value: 'jax', label: 'JAX', description: 'NumPy-compatible ML research framework' }
  ];

  const papers = [
    { id: 1, title: 'Deep Learning Approaches for Climate Modeling', domain: 'Climate Science' },
    { id: 2, title: 'Quantum Computing Applications in Drug Discovery', domain: 'Quantum Computing' },
    { id: 3, title: 'Machine Learning for Protein Folding Prediction', domain: 'Bioinformatics' },
    { id: 4, title: 'Computer Vision for Medical Image Analysis', domain: 'Medical AI' },
    { id: 5, title: 'Natural Language Processing for Scientific Literature', domain: 'NLP' }
  ];

  const regions = [
    { value: 'us-east-1', label: 'US East (N. Virginia)' },
    { value: 'us-west-2', label: 'US West (Oregon)' },
    { value: 'eu-west-1', label: 'Europe (Ireland)' },
    { value: 'ap-southeast-1', label: 'Asia Pacific (Singapore)' }
  ];

  const optimizers = ['adam', 'sgd', 'rmsprop', 'adagrad', 'adadelta'];

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to training models page
      window.location.href = '/dashboard/models/training';
    }, 2000);
  };

  const nextStep = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const getStepStatus = (step) => {
    if (step < currentStep) return 'completed';
    if (step === currentStep) return 'current';
    return 'upcoming';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <DashboardLayout>
      <div className="pt-16 sm:pt-20 px-4 lg:px-6">
        {/* Header */}
        <motion.div
          className="mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-accent">Create New Model</h1>
              <p className="text-sm lg:text-base text-primary mt-1">
                Configure and train a new AI model from your research papers
              </p>
            </div>
            <div className="mt-4 lg:mt-0">
              <Link
                to="/dashboard/models"
                className="inline-flex items-center px-4 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200 text-sm lg:text-base"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
                Back to Models
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Progress Steps */}
        <motion.div
          className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-gray-100 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            {[
              { step: 1, title: 'Basic Info', description: 'Model details' },
              { step: 2, title: 'Configuration', description: 'Type & framework' },
              { step: 3, title: 'Parameters', description: 'Hyperparameters' },
              { step: 4, title: 'Deployment', description: 'Deploy settings' }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center w-full sm:w-auto">
                <div className="flex flex-col items-center w-full">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    getStepStatus(item.step) === 'completed'
                      ? 'bg-green-500 text-white'
                      : getStepStatus(item.step) === 'current'
                      ? 'bg-highlight text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {getStepStatus(item.step) === 'completed' ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      item.step
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <div className="text-sm font-medium text-accent">{item.title}</div>
                    <div className="text-xs text-primary">{item.description}</div>
                  </div>
                </div>
                {index < 3 && (
                  <div className="hidden sm:block flex-1 h-0.5 bg-gray-200 mx-4">
                    <div className={`h-full ${
                      getStepStatus(item.step) === 'completed' ? 'bg-green-500' : 'bg-gray-200'
                    }`}></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Form Content */}
        <motion.div
          className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-gray-100"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <form onSubmit={handleSubmit}>
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <h2 className="text-xl font-semibold text-accent mb-6">Basic Information</h2>

                <div className="space-y-6">
                  <motion.div variants={itemVariants}>
                    <label className="block text-sm font-medium text-accent mb-2">
                      Model Name *
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight"
                      placeholder="e.g., Climate_Prediction_CNN_v1"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Choose a descriptive name for your model</p>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <label className="block text-sm font-medium text-accent mb-2">
                      Description *
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight"
                      rows="4"
                      placeholder="Describe what your model does and its intended use case..."
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      required
                    />
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <label className="block text-sm font-medium text-accent mb-2">
                      Source Paper *
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-highlight focus:border-highlight"
                      value={formData.paperId}
                      onChange={(e) => handleInputChange('paperId', e.target.value)}
                      required
                    >
                      <option value="">Select a paper...</option>
                      {papers.map(paper => (
                        <option key={paper.id} value={paper.id}>
                          {paper.title} ({paper.domain})
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">Choose the research paper this model is based on</p>
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Step 2: Model Configuration */}
            {currentStep === 2 && (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <h2 className="text-xl font-semibold text-accent mb-6">Model Configuration</h2>

                <div className="space-y-6">
                  <motion.div variants={itemVariants}>
                    <label className="block text-sm font-medium text-accent mb-4">
                      Model Type *
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {modelTypes.map(type => (
                        <div
                          key={type.value}
                          className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                            formData.modelType === type.value
                              ? 'border-highlight bg-blue-50'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                          onClick={() => handleInputChange('modelType', type.value)}
                        >
                          <div className="flex items-center mb-2">
                            <input
                              type="radio"
                              name="modelType"
                              value={type.value}
                              checked={formData.modelType === type.value}
                              onChange={() => handleInputChange('modelType', type.value)}
                              className="mr-3"
                            />
                            <span className="font-medium text-accent">{type.label}</span>
                          </div>
                          <p className="text-sm text-gray-600 ml-6">{type.description}</p>
                        </div>
                      ))}
                    </div>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <label className="block text-sm font-medium text-accent mb-4">
                      Framework *
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {frameworks.map(framework => (
                        <div
                          key={framework.value}
                          className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                            formData.framework === framework.value
                              ? 'border-highlight bg-blue-50'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                          onClick={() => handleInputChange('framework', framework.value)}
                        >
                          <div className="flex items-center mb-2">
                            <input
                              type="radio"
                              name="framework"
                              value={framework.value}
                              checked={formData.framework === framework.value}
                              onChange={() => handleInputChange('framework', framework.value)}
                              className="mr-3"
                            />
                            <span className="font-medium text-accent">{framework.label}</span>
                          </div>
                          <p className="text-sm text-gray-600 ml-6">{framework.description}</p>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Navigation Buttons */}
            <div className="flex flex-col sm:flex-row justify-between mt-8 space-y-4 sm:space-y-0">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={prevStep}
                  className="px-6 py-2 bg-gray-100 text-accent rounded-lg font-medium hover:bg-gray-200 transition-all duration-200"
                >
                  Previous
                </button>
              )}
              <div className="flex justify-end">
                {currentStep < 4 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    className="px-6 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-highlight text-white rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 disabled:opacity-50"
                  >
                    {isSubmitting ? 'Creating...' : 'Create Model'}
                  </button>
                )}
              </div>
            </div>
          </form>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default CreateModel;